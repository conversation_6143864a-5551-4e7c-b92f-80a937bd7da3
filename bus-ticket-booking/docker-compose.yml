version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: bus-booking-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: bus_ticket_booking
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - bus-booking-network

  redis:
    image: redis:7-alpine
    container_name: bus-booking-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - bus-booking-network

  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: bus-booking-app
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      NODE_ENV: development
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USERNAME: postgres
      DB_PASSWORD: password
      DB_NAME: bus_ticket_booking
      REDIS_HOST: redis
      REDIS_PORT: 6379
    depends_on:
      - postgres
      - redis
    networks:
      - bus-booking-network
    volumes:
      - .:/app
      - /app/node_modules

volumes:
  postgres_data:
  redis_data:

networks:
  bus-booking-network:
    driver: bridge
