{"version": 3, "sources": ["../browser/src/repository/TreeRepository.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,WAAW,EAAE,MAAM,uBAAuB,CAAA;AACnD,OAAO,EAAE,YAAY,EAAE,MAAM,uBAAuB,CAAA;AACpD,OAAO,EAAE,gBAAgB,EAAE,MAAM,kCAAkC,CAAA;AAGnE,OAAO,EAAE,mBAAmB,EAAE,MAAM,6BAA6B,CAAA;AACjE,OAAO,EAAE,UAAU,EAAE,MAAM,cAAc,CAAA;AAEzC;;;;GAIG;AACH,MAAM,OAAO,cAEX,SAAQ,UAAkB;IACxB,4EAA4E;IAC5E,iBAAiB;IACjB,4EAA4E;IAE5E;;OAEG;IACH,KAAK,CAAC,SAAS,CAAC,OAAyB;QACrC,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;QAC3C,MAAM,OAAO,CAAC,GAAG,CACb,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAC/D,CAAA;QACD,OAAO,KAAK,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,SAAS,CAAC,OAAyB;QAC/B,MAAM,WAAW,GAAG,CAAC,KAAa,EAAE,EAAE,CAClC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;QAChD,MAAM,YAAY,GAAG,CAAC,MAAc,EAAE,EAAE,CACpC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA;QAEjD,MAAM,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,kBAAmB,CAAC,WAAW,CAAC,CAAC,CAAC,CAAA;QACnE,MAAM,kBAAkB,GACpB,UAAU,CAAC,iBAAiB,IAAI,UAAU,CAAC,YAAY,CAAA;QAE3D,MAAM,EAAE,GAAG,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,CAAA;QAChD,gBAAgB,CAAC,8BAA8B,CAAC,EAAE,EAAE,OAAO,CAAC,CAAA;QAE5D,OAAO,EAAE;aACJ,KAAK,CACF,GAAG,WAAW,CAAC,YAAY,CAAC,IAAI,YAAY,CACxC,kBAAkB,CACrB,UAAU,CACd;aACA,OAAO,EAAE,CAAA;IAClB,CAAC;IAED;;OAEG;IACH,eAAe,CACX,MAAc,EACd,OAAyB;QAEzB,MAAM,EAAE,GAAG,IAAI,CAAC,6BAA6B,CACzC,YAAY,EACZ,aAAa,EACb,MAAM,CACT,CAAA;QACD,gBAAgB,CAAC,8BAA8B,CAAC,EAAE,EAAE,OAAO,CAAC,CAAA;QAC5D,OAAO,EAAE,CAAC,OAAO,EAAE,CAAA;IACvB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,mBAAmB,CACrB,MAAc,EACd,OAAyB;QAEzB,gEAAgE;QAEhE,MAAM,EAAE,GACJ,IAAI,CAAC,6BAA6B,CAC9B,YAAY,EACZ,aAAa,EACb,MAAM,CACT,CAAA;QACL,gBAAgB,CAAC,8BAA8B,CAAC,EAAE,EAAE,OAAO,CAAC,CAAA;QAE5D,MAAM,QAAQ,GAAG,MAAM,EAAE,CAAC,iBAAiB,EAAE,CAAA;QAC7C,MAAM,YAAY,GAAG,mBAAmB,CAAC,kBAAkB,CACvD,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,QAAQ,EACb,YAAY,EACZ,QAAQ,CAAC,GAAG,CACf,CAAA;QACD,mBAAmB,CAAC,uBAAuB,CACvC,IAAI,CAAC,QAAQ,EACb,MAAM,EACN,QAAQ,CAAC,QAAQ,EACjB,YAAY,EACZ;YACI,KAAK,EAAE,CAAC,CAAC;YACT,GAAG,OAAO;SACb,CACJ,CAAA;QAED,OAAO,MAAM,CAAA;IACjB,CAAC;IAED;;OAEG;IACH,gBAAgB,CAAC,MAAc;QAC3B,OAAO,IAAI,CAAC,6BAA6B,CACrC,YAAY,EACZ,aAAa,EACb,MAAM,CACT,CAAC,QAAQ,EAAE,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,6BAA6B,CACzB,KAAa,EACb,iBAAyB,EACzB,MAAc;QAEd,0CAA0C;QAC1C,MAAM,MAAM,GAAG,CAAC,KAAa,EAAE,EAAE,CAC7B,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;QAEhD,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,KAAK,eAAe,EAAE,CAAC;YAC7C,MAAM,aAAa,GACf,IAAI,CAAC,QAAQ,CAAC,oBAAoB,CAAC,iBAAiB;iBAC/C,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;gBACZ,OAAO,CACH,MAAM,CAAC,iBAAiB,CAAC;oBACzB,GAAG;oBACH,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;oBAC3B,KAAK;oBACL,MAAM,CAAC,KAAK,CAAC;oBACb,GAAG;oBACH,MAAM,CAAC,MAAM,CAAC,gBAAiB,CAAC,YAAY,CAAC,CAChD,CAAA;YACL,CAAC,CAAC;iBACD,IAAI,CAAC,OAAO,CAAC,CAAA;YAEtB,MAAM,UAAU,GAAkB,EAAE,CAAA;YACpC,MAAM,cAAc,GAChB,IAAI,CAAC,QAAQ,CAAC,oBAAoB,CAAC,eAAe;iBAC7C,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;gBACZ,UAAU,CAAC,MAAM,CAAC,gBAAiB,CAAC,YAAY,CAAC;oBAC7C,MAAM,CAAC,gBAAiB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;gBACnD,OAAO,CACH,MAAM,CAAC,iBAAiB,CAAC;oBACzB,GAAG;oBACH,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;oBAC3B,MAAM;oBACN,MAAM,CAAC,gBAAiB,CAAC,YAAY,CACxC,CAAA;YACL,CAAC,CAAC;iBACD,IAAI,CAAC,OAAO,CAAC,CAAA;YAEtB,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;iBAChC,SAAS,CACN,IAAI,CAAC,QAAQ,CAAC,oBAAoB,CAAC,SAAS,EAC5C,iBAAiB,EACjB,aAAa,CAChB;iBACA,KAAK,CAAC,cAAc,CAAC;iBACrB,aAAa,CAAC,UAAU,CAAC,CAAA;QAClC,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;YACjD,MAAM,cAAc,GAChB,KAAK;gBACL,GAAG;gBACH,IAAI,CAAC,QAAQ,CAAC,mBAAoB,CAAC,YAAY;gBAC/C,WAAW;gBACX,SAAS;gBACT,IAAI,CAAC,QAAQ,CAAC,mBAAoB,CAAC,YAAY;gBAC/C,cAAc;gBACd,IAAI,CAAC,QAAQ,CAAC,oBAAqB,CAAC,YAAY,CAAA;YACpD,MAAM,UAAU,GAAkB,EAAE,CAAA;YACpC,MAAM,aAAa,GAAG,IAAI,CAAC,QAAQ;iBAC9B,kBAAmB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;gBAChD,MAAM,aAAa,GACf,UAAU,CAAC,gBAAiB,CAAC,YAAY,CAAC,OAAO,CAC7C,GAAG,EACH,GAAG,CACN,CAAA;gBACL,UAAU,CAAC,aAAa,CAAC;oBACrB,UAAU,CAAC,gBAAiB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;gBACvD,OAAO,CACH,SAAS;oBACT,UAAU,CAAC,gBAAiB,CAAC,YAAY;oBACzC,MAAM;oBACN,aAAa,CAChB,CAAA;YACL,CAAC,CAAC;iBACD,IAAI,CAAC,OAAO,CAAC,CAAA;YAElB,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;iBAChC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,QAAQ,EAAE,cAAc,CAAC;iBAC7D,KAAK,CAAC,aAAa,EAAE,UAAU,CAAC,CAAA;QACzC,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,KAAK,mBAAmB,EAAE,CAAC;YACxD,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,EAAE;gBAC/C,MAAM,QAAQ,GAAG,EAAE;qBACd,QAAQ,EAAE;qBACV,MAAM,CACH,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,IACvB,IAAI,CAAC,QAAQ,CAAC,sBAAuB,CAAC,YAC1C,EAAE,EACF,MAAM,CACT;qBACA,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;qBACpD,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAA;gBAErD,IACI,WAAW,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,EAC5D,CAAC;oBACC,OAAO,GAAG,KAAK,IACX,IAAI,CAAC,QAAQ,CAAC,sBAAuB,CAAC,YAC1C,SAAS,QAAQ,CAAC,QAAQ,EAAE,SAAS,CAAA;gBACzC,CAAC;qBAAM,CAAC;oBACJ,OAAO,GAAG,KAAK,IACX,IAAI,CAAC,QAAQ,CAAC,sBAAuB,CAAC,YAC1C,uBAAuB,QAAQ,CAAC,QAAQ,EAAE,cAAc,CAAA;gBAC5D,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;QAED,MAAM,IAAI,YAAY,CAAC,iCAAiC,CAAC,CAAA;IAC7D,CAAC;IAED;;OAEG;IACH,aAAa,CACT,MAAc,EACd,OAAyB;QAEzB,MAAM,EAAE,GAAG,IAAI,CAAC,2BAA2B,CACvC,YAAY,EACZ,aAAa,EACb,MAAM,CACT,CAAA;QACD,gBAAgB,CAAC,8BAA8B,CAAC,EAAE,EAAE,OAAO,CAAC,CAAA;QAC5D,OAAO,EAAE,CAAC,OAAO,EAAE,CAAA;IACvB,CAAC;IAED;;OAEG;IACH,KAAK,CAAC,iBAAiB,CACnB,MAAc,EACd,OAAyB;QAEzB,gEAAgE;QAChE,MAAM,EAAE,GAAG,IAAI,CAAC,2BAA2B,CACvC,YAAY,EACZ,aAAa,EACb,MAAM,CACT,CAAA;QACD,gBAAgB,CAAC,8BAA8B,CAAC,EAAE,EAAE,OAAO,CAAC,CAAA;QAE5D,MAAM,QAAQ,GAAG,MAAM,EAAE,CAAC,iBAAiB,EAAE,CAAA;QAC7C,MAAM,YAAY,GAAG,mBAAmB,CAAC,kBAAkB,CACvD,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,QAAQ,EACb,YAAY,EACZ,QAAQ,CAAC,GAAG,CACf,CAAA;QACD,mBAAmB,CAAC,qBAAqB,CACrC,IAAI,CAAC,QAAQ,EACb,MAAM,EACN,QAAQ,CAAC,QAAQ,EACjB,YAAY,CACf,CAAA;QACD,OAAO,MAAM,CAAA;IACjB,CAAC;IAED;;OAEG;IACH,cAAc,CAAC,MAAc;QACzB,OAAO,IAAI,CAAC,2BAA2B,CACnC,YAAY,EACZ,aAAa,EACb,MAAM,CACT,CAAC,QAAQ,EAAE,CAAA;IAChB,CAAC;IAED;;OAEG;IACH,2BAA2B,CACvB,KAAa,EACb,iBAAyB,EACzB,MAAc;QAEd,0CAA0C;QAC1C,kFAAkF;QAElF,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,KAAK,eAAe,EAAE,CAAC;YAC7C,MAAM,aAAa,GACf,IAAI,CAAC,QAAQ,CAAC,oBAAoB,CAAC,eAAe;iBAC7C,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;gBACZ,OAAO,CACH,iBAAiB;oBACjB,GAAG;oBACH,MAAM,CAAC,YAAY;oBACnB,KAAK;oBACL,KAAK;oBACL,GAAG;oBACH,MAAM,CAAC,gBAAiB,CAAC,YAAY,CACxC,CAAA;YACL,CAAC,CAAC;iBACD,IAAI,CAAC,OAAO,CAAC,CAAA;YAEtB,MAAM,UAAU,GAAkB,EAAE,CAAA;YACpC,MAAM,cAAc,GAChB,IAAI,CAAC,QAAQ,CAAC,oBAAoB,CAAC,iBAAiB;iBAC/C,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE;gBACZ,UAAU,CAAC,MAAM,CAAC,gBAAiB,CAAC,YAAY,CAAC;oBAC7C,MAAM,CAAC,gBAAiB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;gBACnD,OAAO,CACH,iBAAiB;oBACjB,GAAG;oBACH,MAAM,CAAC,YAAY;oBACnB,MAAM;oBACN,MAAM,CAAC,gBAAiB,CAAC,YAAY,CACxC,CAAA;YACL,CAAC,CAAC;iBACD,IAAI,CAAC,OAAO,CAAC,CAAA;YAEtB,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;iBAChC,SAAS,CACN,IAAI,CAAC,QAAQ,CAAC,oBAAoB,CAAC,SAAS,EAC5C,iBAAiB,EACjB,aAAa,CAChB;iBACA,KAAK,CAAC,cAAc,CAAC;iBACrB,aAAa,CAAC,UAAU,CAAC,CAAA;QAClC,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,KAAK,YAAY,EAAE,CAAC;YACjD,MAAM,aAAa,GACf,SAAS;gBACT,IAAI,CAAC,QAAQ,CAAC,mBAAoB,CAAC,YAAY;gBAC/C,WAAW;gBACX,KAAK;gBACL,GAAG;gBACH,IAAI,CAAC,QAAQ,CAAC,mBAAoB,CAAC,YAAY;gBAC/C,OAAO;gBACP,KAAK;gBACL,GAAG;gBACH,IAAI,CAAC,QAAQ,CAAC,oBAAqB,CAAC,YAAY,CAAA;YACpD,MAAM,UAAU,GAAkB,EAAE,CAAA;YACpC,MAAM,cAAc,GAAG,IAAI,CAAC,QAAQ;iBAC/B,kBAAmB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,EAAE;gBAChD,MAAM,aAAa,GACf,UAAU,CAAC,gBAAiB,CAAC,YAAY,CAAC,OAAO,CAC7C,GAAG,EACH,GAAG,CACN,CAAA;gBACL,UAAU,CAAC,aAAa,CAAC;oBACrB,UAAU,CAAC,gBAAiB,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;gBACvD,OAAO,CACH,SAAS;oBACT,UAAU,CAAC,gBAAiB,CAAC,YAAY;oBACzC,MAAM;oBACN,aAAa,CAChB,CAAA;YACL,CAAC,CAAC;iBACD,IAAI,CAAC,OAAO,CAAC,CAAA;YAElB,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC;iBAChC,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE,QAAQ,EAAE,aAAa,CAAC;iBAC5D,KAAK,CAAC,cAAc,EAAE,UAAU,CAAC,CAAA;QAC1C,CAAC;aAAM,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,KAAK,mBAAmB,EAAE,CAAC;YACxD,+HAA+H;YAC/H,OAAO,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,EAAE;gBAC/C,MAAM,QAAQ,GAAG,EAAE;qBACd,QAAQ,EAAE;qBACV,MAAM,CACH,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,IACvB,IAAI,CAAC,QAAQ,CAAC,sBAAuB,CAAC,YAC1C,EAAE,EACF,MAAM,CACT;qBACA,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC;qBACpD,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAA;gBAErD,IACI,WAAW,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,MAAM,CAAC,EAC5D,CAAC;oBACC,OAAO,GAAG,QAAQ,CAAC,QAAQ,EAAE,SAAS,KAAK,IACvC,IAAI,CAAC,QAAQ,CAAC,sBAAuB,CAAC,YAC1C,SAAS,CAAA;gBACb,CAAC;qBAAM,CAAC;oBACJ,OAAO,GAAG,QAAQ,CAAC,QAAQ,EAAE,gBAAgB,KAAK,IAC9C,IAAI,CAAC,QAAQ,CAAC,sBAAuB,CAAC,YAC1C,QAAQ,CAAA;gBACZ,CAAC;YACL,CAAC,CAAC,CAAA;QACN,CAAC;QAED,MAAM,IAAI,YAAY,CAAC,iCAAiC,CAAC,CAAA;IAC7D,CAAC;CAQJ", "file": "TreeRepository.js", "sourcesContent": ["import { ObjectLiteral } from \"../common/ObjectLiteral\"\nimport { DriverUtils } from \"../driver/DriverUtils\"\nimport { TypeORMError } from \"../error/TypeORMError\"\nimport { FindOptionsUtils } from \"../find-options/FindOptionsUtils\"\nimport { FindTreeOptions } from \"../find-options/FindTreeOptions\"\nimport { SelectQueryBuilder } from \"../query-builder/SelectQueryBuilder\"\nimport { TreeRepositoryUtils } from \"../util/TreeRepositoryUtils\"\nimport { Repository } from \"./Repository\"\n\n/**\n * Repository with additional functions to work with trees.\n *\n * @see Repository\n */\nexport class TreeRepository<\n    Entity extends ObjectLiteral,\n> extends Repository<Entity> {\n    // -------------------------------------------------------------------------\n    // Public Methods\n    // -------------------------------------------------------------------------\n\n    /**\n     * Gets complete trees for all roots in the table.\n     */\n    async findTrees(options?: FindTreeOptions): Promise<Entity[]> {\n        const roots = await this.findRoots(options)\n        await Promise.all(\n            roots.map((root) => this.findDescendantsTree(root, options)),\n        )\n        return roots\n    }\n\n    /**\n     * Roots are entities that have no ancestors. Finds them all.\n     */\n    findRoots(options?: FindTreeOptions): Promise<Entity[]> {\n        const escapeAlias = (alias: string) =>\n            this.manager.connection.driver.escape(alias)\n        const escapeColumn = (column: string) =>\n            this.manager.connection.driver.escape(column)\n\n        const joinColumn = this.metadata.treeParentRelation!.joinColumns[0]\n        const parentPropertyName =\n            joinColumn.givenDatabaseName || joinColumn.databaseName\n\n        const qb = this.createQueryBuilder(\"treeEntity\")\n        FindOptionsUtils.applyOptionsToTreeQueryBuilder(qb, options)\n\n        return qb\n            .where(\n                `${escapeAlias(\"treeEntity\")}.${escapeColumn(\n                    parentPropertyName,\n                )} IS NULL`,\n            )\n            .getMany()\n    }\n\n    /**\n     * Gets all children (descendants) of the given entity. Returns them all in a flat array.\n     */\n    findDescendants(\n        entity: Entity,\n        options?: FindTreeOptions,\n    ): Promise<Entity[]> {\n        const qb = this.createDescendantsQueryBuilder(\n            \"treeEntity\",\n            \"treeClosure\",\n            entity,\n        )\n        FindOptionsUtils.applyOptionsToTreeQueryBuilder(qb, options)\n        return qb.getMany()\n    }\n\n    /**\n     * Gets all children (descendants) of the given entity. Returns them in a tree - nested into each other.\n     */\n    async findDescendantsTree(\n        entity: Entity,\n        options?: FindTreeOptions,\n    ): Promise<Entity> {\n        // todo: throw exception if there is no column of this relation?\n\n        const qb: SelectQueryBuilder<Entity> =\n            this.createDescendantsQueryBuilder(\n                \"treeEntity\",\n                \"treeClosure\",\n                entity,\n            )\n        FindOptionsUtils.applyOptionsToTreeQueryBuilder(qb, options)\n\n        const entities = await qb.getRawAndEntities()\n        const relationMaps = TreeRepositoryUtils.createRelationMaps(\n            this.manager,\n            this.metadata,\n            \"treeEntity\",\n            entities.raw,\n        )\n        TreeRepositoryUtils.buildChildrenEntityTree(\n            this.metadata,\n            entity,\n            entities.entities,\n            relationMaps,\n            {\n                depth: -1,\n                ...options,\n            },\n        )\n\n        return entity\n    }\n\n    /**\n     * Gets number of descendants of the entity.\n     */\n    countDescendants(entity: Entity): Promise<number> {\n        return this.createDescendantsQueryBuilder(\n            \"treeEntity\",\n            \"treeClosure\",\n            entity,\n        ).getCount()\n    }\n\n    /**\n     * Creates a query builder used to get descendants of the entities in a tree.\n     */\n    createDescendantsQueryBuilder(\n        alias: string,\n        closureTableAlias: string,\n        entity: Entity,\n    ): SelectQueryBuilder<Entity> {\n        // create shortcuts for better readability\n        const escape = (alias: string) =>\n            this.manager.connection.driver.escape(alias)\n\n        if (this.metadata.treeType === \"closure-table\") {\n            const joinCondition =\n                this.metadata.closureJunctionTable.descendantColumns\n                    .map((column) => {\n                        return (\n                            escape(closureTableAlias) +\n                            \".\" +\n                            escape(column.propertyPath) +\n                            \" = \" +\n                            escape(alias) +\n                            \".\" +\n                            escape(column.referencedColumn!.propertyPath)\n                        )\n                    })\n                    .join(\" AND \")\n\n            const parameters: ObjectLiteral = {}\n            const whereCondition =\n                this.metadata.closureJunctionTable.ancestorColumns\n                    .map((column) => {\n                        parameters[column.referencedColumn!.propertyName] =\n                            column.referencedColumn!.getEntityValue(entity)\n                        return (\n                            escape(closureTableAlias) +\n                            \".\" +\n                            escape(column.propertyPath) +\n                            \" = :\" +\n                            column.referencedColumn!.propertyName\n                        )\n                    })\n                    .join(\" AND \")\n\n            return this.createQueryBuilder(alias)\n                .innerJoin(\n                    this.metadata.closureJunctionTable.tableName,\n                    closureTableAlias,\n                    joinCondition,\n                )\n                .where(whereCondition)\n                .setParameters(parameters)\n        } else if (this.metadata.treeType === \"nested-set\") {\n            const whereCondition =\n                alias +\n                \".\" +\n                this.metadata.nestedSetLeftColumn!.propertyPath +\n                \" BETWEEN \" +\n                \"joined.\" +\n                this.metadata.nestedSetLeftColumn!.propertyPath +\n                \" AND joined.\" +\n                this.metadata.nestedSetRightColumn!.propertyPath\n            const parameters: ObjectLiteral = {}\n            const joinCondition = this.metadata\n                .treeParentRelation!.joinColumns.map((joinColumn) => {\n                    const parameterName =\n                        joinColumn.referencedColumn!.propertyPath.replace(\n                            \".\",\n                            \"_\",\n                        )\n                    parameters[parameterName] =\n                        joinColumn.referencedColumn!.getEntityValue(entity)\n                    return (\n                        \"joined.\" +\n                        joinColumn.referencedColumn!.propertyPath +\n                        \" = :\" +\n                        parameterName\n                    )\n                })\n                .join(\" AND \")\n\n            return this.createQueryBuilder(alias)\n                .innerJoin(this.metadata.targetName, \"joined\", whereCondition)\n                .where(joinCondition, parameters)\n        } else if (this.metadata.treeType === \"materialized-path\") {\n            return this.createQueryBuilder(alias).where((qb) => {\n                const subQuery = qb\n                    .subQuery()\n                    .select(\n                        `${this.metadata.targetName}.${\n                            this.metadata.materializedPathColumn!.propertyPath\n                        }`,\n                        \"path\",\n                    )\n                    .from(this.metadata.target, this.metadata.targetName)\n                    .whereInIds(this.metadata.getEntityIdMap(entity))\n\n                if (\n                    DriverUtils.isSQLiteFamily(this.manager.connection.driver)\n                ) {\n                    return `${alias}.${\n                        this.metadata.materializedPathColumn!.propertyPath\n                    } LIKE ${subQuery.getQuery()} || '%'`\n                } else {\n                    return `${alias}.${\n                        this.metadata.materializedPathColumn!.propertyPath\n                    } LIKE NULLIF(CONCAT(${subQuery.getQuery()}, '%'), '%')`\n                }\n            })\n        }\n\n        throw new TypeORMError(`Supported only in tree entities`)\n    }\n\n    /**\n     * Gets all parents (ancestors) of the given entity. Returns them all in a flat array.\n     */\n    findAncestors(\n        entity: Entity,\n        options?: FindTreeOptions,\n    ): Promise<Entity[]> {\n        const qb = this.createAncestorsQueryBuilder(\n            \"treeEntity\",\n            \"treeClosure\",\n            entity,\n        )\n        FindOptionsUtils.applyOptionsToTreeQueryBuilder(qb, options)\n        return qb.getMany()\n    }\n\n    /**\n     * Gets all parents (ancestors) of the given entity. Returns them in a tree - nested into each other.\n     */\n    async findAncestorsTree(\n        entity: Entity,\n        options?: FindTreeOptions,\n    ): Promise<Entity> {\n        // todo: throw exception if there is no column of this relation?\n        const qb = this.createAncestorsQueryBuilder(\n            \"treeEntity\",\n            \"treeClosure\",\n            entity,\n        )\n        FindOptionsUtils.applyOptionsToTreeQueryBuilder(qb, options)\n\n        const entities = await qb.getRawAndEntities()\n        const relationMaps = TreeRepositoryUtils.createRelationMaps(\n            this.manager,\n            this.metadata,\n            \"treeEntity\",\n            entities.raw,\n        )\n        TreeRepositoryUtils.buildParentEntityTree(\n            this.metadata,\n            entity,\n            entities.entities,\n            relationMaps,\n        )\n        return entity\n    }\n\n    /**\n     * Gets number of ancestors of the entity.\n     */\n    countAncestors(entity: Entity): Promise<number> {\n        return this.createAncestorsQueryBuilder(\n            \"treeEntity\",\n            \"treeClosure\",\n            entity,\n        ).getCount()\n    }\n\n    /**\n     * Creates a query builder used to get ancestors of the entities in the tree.\n     */\n    createAncestorsQueryBuilder(\n        alias: string,\n        closureTableAlias: string,\n        entity: Entity,\n    ): SelectQueryBuilder<Entity> {\n        // create shortcuts for better readability\n        // const escape = (alias: string) => this.manager.connection.driver.escape(alias);\n\n        if (this.metadata.treeType === \"closure-table\") {\n            const joinCondition =\n                this.metadata.closureJunctionTable.ancestorColumns\n                    .map((column) => {\n                        return (\n                            closureTableAlias +\n                            \".\" +\n                            column.propertyPath +\n                            \" = \" +\n                            alias +\n                            \".\" +\n                            column.referencedColumn!.propertyPath\n                        )\n                    })\n                    .join(\" AND \")\n\n            const parameters: ObjectLiteral = {}\n            const whereCondition =\n                this.metadata.closureJunctionTable.descendantColumns\n                    .map((column) => {\n                        parameters[column.referencedColumn!.propertyName] =\n                            column.referencedColumn!.getEntityValue(entity)\n                        return (\n                            closureTableAlias +\n                            \".\" +\n                            column.propertyPath +\n                            \" = :\" +\n                            column.referencedColumn!.propertyName\n                        )\n                    })\n                    .join(\" AND \")\n\n            return this.createQueryBuilder(alias)\n                .innerJoin(\n                    this.metadata.closureJunctionTable.tableName,\n                    closureTableAlias,\n                    joinCondition,\n                )\n                .where(whereCondition)\n                .setParameters(parameters)\n        } else if (this.metadata.treeType === \"nested-set\") {\n            const joinCondition =\n                \"joined.\" +\n                this.metadata.nestedSetLeftColumn!.propertyPath +\n                \" BETWEEN \" +\n                alias +\n                \".\" +\n                this.metadata.nestedSetLeftColumn!.propertyPath +\n                \" AND \" +\n                alias +\n                \".\" +\n                this.metadata.nestedSetRightColumn!.propertyPath\n            const parameters: ObjectLiteral = {}\n            const whereCondition = this.metadata\n                .treeParentRelation!.joinColumns.map((joinColumn) => {\n                    const parameterName =\n                        joinColumn.referencedColumn!.propertyPath.replace(\n                            \".\",\n                            \"_\",\n                        )\n                    parameters[parameterName] =\n                        joinColumn.referencedColumn!.getEntityValue(entity)\n                    return (\n                        \"joined.\" +\n                        joinColumn.referencedColumn!.propertyPath +\n                        \" = :\" +\n                        parameterName\n                    )\n                })\n                .join(\" AND \")\n\n            return this.createQueryBuilder(alias)\n                .innerJoin(this.metadata.targetName, \"joined\", joinCondition)\n                .where(whereCondition, parameters)\n        } else if (this.metadata.treeType === \"materialized-path\") {\n            // example: SELECT * FROM category category WHERE (SELECT mpath FROM `category` WHERE id = 2) LIKE CONCAT(category.mpath, '%');\n            return this.createQueryBuilder(alias).where((qb) => {\n                const subQuery = qb\n                    .subQuery()\n                    .select(\n                        `${this.metadata.targetName}.${\n                            this.metadata.materializedPathColumn!.propertyPath\n                        }`,\n                        \"path\",\n                    )\n                    .from(this.metadata.target, this.metadata.targetName)\n                    .whereInIds(this.metadata.getEntityIdMap(entity))\n\n                if (\n                    DriverUtils.isSQLiteFamily(this.manager.connection.driver)\n                ) {\n                    return `${subQuery.getQuery()} LIKE ${alias}.${\n                        this.metadata.materializedPathColumn!.propertyPath\n                    } || '%'`\n                } else {\n                    return `${subQuery.getQuery()} LIKE CONCAT(${alias}.${\n                        this.metadata.materializedPathColumn!.propertyPath\n                    }, '%')`\n                }\n            })\n        }\n\n        throw new TypeORMError(`Supported only in tree entities`)\n    }\n\n    /**\n     * Moves entity to the children of then given entity.\n     *\n    move(entity: Entity, to: Entity): Promise<void> {\n        return Promise.resolve();\n    } */\n}\n"], "sourceRoot": ".."}