{"version": 3, "sources": ["../browser/src/metadata-args/TreeMetadataArgs.ts"], "names": [], "mappings": "", "file": "TreeMetadataArgs.js", "sourcesContent": ["import { TreeType } from \"../metadata/types/TreeTypes\"\nimport { ClosureTreeOptions } from \"../metadata/types/ClosureTreeOptions\"\n\n/**\n * Stores metadata collected for Tree entities.\n */\nexport interface TreeMetadataArgs {\n    /**\n     * Entity to which tree is applied.\n     */\n    target: Function | string\n\n    /**\n     * Tree type.\n     */\n    type: TreeType\n\n    /**\n     * Tree options\n     */\n    options?: ClosureTreeOptions\n}\n"], "sourceRoot": ".."}