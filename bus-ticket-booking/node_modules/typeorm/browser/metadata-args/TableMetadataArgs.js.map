{"version": 3, "sources": ["../browser/src/metadata-args/TableMetadataArgs.ts"], "names": [], "mappings": "", "file": "TableMetadataArgs.js", "sourcesContent": ["import { DataSource, SelectQueryBuilder } from \"..\"\nimport { OrderByCondition } from \"../find-options/OrderByCondition\"\nimport { TableType } from \"../metadata/types/TableTypes\"\n\n/**\n * Arguments for TableMetadata class, helps to construct an TableMetadata object.\n */\nexport interface TableMetadataArgs {\n    /**\n     * Class to which table is applied.\n     * Function target is a table defined in the class.\n     * String target is a table defined in a json schema.\n     */\n    target: Function | string\n\n    /**\n     * Table's name. If name is not set then table's name will be generated from target's name.\n     */\n    name?: string\n\n    /**\n     * Table type. Tables can be abstract, closure, junction, embedded, etc.\n     */\n    type: TableType\n\n    /**\n     * Specifies a default order by used for queries from this table when no explicit order by is specified.\n     */\n    orderBy?: OrderByCondition | ((object: any) => OrderByCondition | any)\n\n    /**\n     * Table's database engine type (like \"InnoDB\", \"MyISAM\", etc).\n     */\n    engine?: string\n\n    /**\n     * Database name. Used in MySql and Sql Server.\n     */\n    database?: string\n\n    /**\n     * Schema name. Used in Postgres and Sql Server.\n     */\n    schema?: string\n\n    /**\n     * Indicates if schema synchronization is enabled or disabled for this entity.\n     * If it will be set to false then schema sync will and migrations ignore this entity.\n     * By default schema synchronization is enabled for all entities.\n     */\n    synchronize?: boolean\n\n    /**\n     * View expression.\n     */\n    expression?: string | ((connection: DataSource) => SelectQueryBuilder<any>)\n\n    /**\n     * View dependencies.\n     */\n    dependsOn?: Set<Function | string>\n\n    /**\n     * Indicates if view is materialized\n     */\n    materialized?: boolean\n\n    /**\n     * If set to 'true' this option disables Sqlite's default behaviour of secretly creating\n     * an integer primary key column named 'rowid' on table creation.\n     */\n    withoutRowid?: boolean\n\n    /**\n     * Table comment. Not supported by all database types.\n     */\n    comment?: string\n}\n"], "sourceRoot": ".."}