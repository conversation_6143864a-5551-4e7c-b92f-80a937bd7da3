{"version": 3, "sources": ["../browser/src/schema-builder/util/ViewUtils.ts"], "names": [], "mappings": "AAEA,MAAM,OAAO,SAAS;IAClB;;OAEG;IACH,MAAM,CAAC,eAAe,CAClB,SAAqC,EACrC,SAAqC;QAErC,IAAI,CAAC,SAAS,IAAI,CAAC,SAAS,EAAE,CAAC;YAC3B,OAAO,CAAC,CAAA;QACZ,CAAC;QACD,IACI,SAAS,CAAC,SAAS;YACnB,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC;gBACtC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAC9C,CAAC;YACC,OAAO,CAAC,CAAA;QACZ,CAAC;QACD,IACI,SAAS,CAAC,SAAS;YACnB,CAAC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,MAAM,CAAC;gBACtC,SAAS,CAAC,SAAS,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAC9C,CAAC;YACC,OAAO,CAAC,CAAC,CAAA;QACb,CAAC;QACD,OAAO,CAAC,CAAA;IACZ,CAAC;CACJ", "file": "ViewUtils.js", "sourcesContent": ["import { EntityMetadata } from \"../../metadata/EntityMetadata\"\n\nexport class ViewUtils {\n    /**\n     * Comparator for .sort() that will order views bases on dependencies in creation order\n     */\n    static viewMetadataCmp(\n        metadataA: EntityMetadata | undefined,\n        metadataB: EntityMetadata | undefined,\n    ): number {\n        if (!metadataA || !metadataB) {\n            return 0\n        }\n        if (\n            metadataA.dependsOn &&\n            (metadataA.dependsOn.has(metadataB.target) ||\n                metadataA.dependsOn.has(metadataB.name))\n        ) {\n            return 1\n        }\n        if (\n            metadataB.dependsOn &&\n            (metadataB.dependsOn.has(metadataA.target) ||\n                metadataB.dependsOn.has(metadataA.name))\n        ) {\n            return -1\n        }\n        return 0\n    }\n}\n"], "sourceRoot": "../.."}