{"version": 3, "sources": ["../../src/decorator/options/VirtualColumnOptions.ts"], "names": [], "mappings": "", "file": "VirtualColumnOptions.js", "sourcesContent": ["import { ColumnType } from \"../../driver/types/ColumnTypes\"\nimport { ValueTransformer } from \"./ValueTransformer\"\n\n/**\n * Describes all calculated column's options.\n */\nexport interface VirtualColumnOptions {\n    /**\n     * Column type. Must be one of the value from the ColumnTypes class.\n     */\n    type?: ColumnType\n\n    /**\n     * Return type of HSTORE column.\n     * Returns value as string or as object.\n     */\n    hstoreType?: \"object\" | \"string\"\n\n    /**\n     * Query to be used to populate the column data. This query is used when generating the relational db script.\n     * The query function is called with the current entities alias either defined by the Entity Decorator or automatically\n     * @See https://typeorm.io/decorator-reference#virtualcolumn for more details.\n     */\n    query: (alias: string) => string\n\n    /**\n     * Specifies a value transformer(s) that is to be used to unmarshal\n     * this column when reading from the database.\n     */\n    transformer?: ValueTransformer | ValueTransformer[]\n}\n"], "sourceRoot": "../.."}