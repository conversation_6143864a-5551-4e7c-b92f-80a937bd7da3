{"version": 3, "sources": ["../../src/decorator/columns/VirtualColumn.ts"], "names": [], "mappings": ";;AAqBA,sCAoDC;AAxED,uCAAsD;AACtD,2CAAsD;AAgBtD;;GAEG;AACH,SAAgB,aAAa,CACzB,aAAiD,EACjD,OAA8B;IAE9B,OAAO,UAAU,MAAc,EAAE,YAAoB;QACjD,uBAAuB;QACvB,IAAI,IAA4B,CAAA;QAChC,IAAI,OAAO,aAAa,KAAK,QAAQ,EAAE,CAAC;YACpC,IAAI,GAAe,aAAa,CAAA;QACpC,CAAC;aAAM,CAAC;YACJ,OAAO,GAAyB,aAAa,CAAA;YAC7C,IAAI,GAAG,OAAO,CAAC,IAAI,CAAA;QACvB,CAAC;QAED,IAAI,CAAC,OAAO,EAAE,KAAK,EAAE,CAAC;YAClB,MAAM,IAAI,KAAK,CACX,wDAAwD,CAC3D,CAAA;QACL,CAAC;QAED,uDAAuD;QACvD,MAAM,mBAAmB,GACrB,OAAO,IAAK,OAAe,CAAC,WAAW;YACnC,CAAC,CAAE,OAAe,CAAC,WAAW,CACxB,aAAa,EACb,MAAM,EACN,YAAY,CACf;YACH,CAAC,CAAC,SAAS,CAAA;QACnB,IAAI,CAAC,IAAI,IAAI,mBAAmB;YAC5B,uDAAuD;YACvD,IAAI,GAAG,mBAAmB,CAAA;QAE9B,yGAAyG;QACzG,IAAI,IAAI;YAAE,OAAO,CAAC,IAAI,GAAG,IAAI,CAAA;QAE7B,0CAA0C;QAC1C,IAAI,OAAO,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,OAAO,CAAC,UAAU;YAChD,OAAO,CAAC,UAAU;gBACd,mBAAmB,KAAK,MAAM,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,CAAA;QAE5D,yFAAyF;QACzF,IAAI,CAAC,OAAO,CAAC,IAAI;YACb,MAAM,IAAI,gCAAwB,CAAC,MAAM,EAAE,YAAY,CAAC,CAAA;QAE5D,IAAA,gCAAsB,GAAE,CAAC,OAAO,CAAC,IAAI,CAAC;YAClC,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,IAAI,EAAE,kBAAkB;YACxB,OAAO,EAAE,OAAO,IAAI,EAAE;SACH,CAAC,CAAA;IAC5B,CAAC,CAAA;AACL,CAAC", "file": "VirtualColumn.js", "sourcesContent": ["import { ColumnType } from \"../../driver/types/ColumnTypes\"\nimport { ColumnTypeUndefinedError } from \"../../error\"\nimport { getMetadataArgsStorage } from \"../../globals\"\nimport { ColumnMetadataArgs } from \"../../metadata-args/ColumnMetadataArgs\"\nimport { VirtualColumnOptions } from \"../options/VirtualColumnOptions\"\n/**\n * VirtualColumn decorator is used to mark a specific class property as a Virtual column.\n */\nexport function VirtualColumn(options: VirtualColumnOptions): PropertyDecorator\n\n/**\n * VirtualColumn decorator is used to mark a specific class property as a Virtual column.\n */\nexport function VirtualColumn(\n    typeOrOptions: ColumnType,\n    options: VirtualColumnOptions,\n): PropertyDecorator\n\n/**\n * VirtualColumn decorator is used to mark a specific class property as a Virtual column.\n */\nexport function VirtualColumn(\n    typeOrOptions?: ColumnType | VirtualColumnOptions,\n    options?: VirtualColumnOptions,\n): PropertyDecorator {\n    return function (object: Object, propertyName: string) {\n        // normalize parameters\n        let type: ColumnType | undefined\n        if (typeof typeOrOptions === \"string\") {\n            type = <ColumnType>typeOrOptions\n        } else {\n            options = <VirtualColumnOptions>typeOrOptions\n            type = options.type\n        }\n\n        if (!options?.query) {\n            throw new Error(\n                \"Column options must be defined for calculated columns.\",\n            )\n        }\n\n        // if type is not given explicitly then try to guess it\n        const reflectMetadataType =\n            Reflect && (Reflect as any).getMetadata\n                ? (Reflect as any).getMetadata(\n                      \"design:type\",\n                      object,\n                      propertyName,\n                  )\n                : undefined\n        if (!type && reflectMetadataType)\n            // if type is not given explicitly then try to guess it\n            type = reflectMetadataType\n\n        // check if there is no type in column options then set type from first function argument, or guessed one\n        if (type) options.type = type\n\n        // specify HSTORE type if column is HSTORE\n        if (options.type === \"hstore\" && !options.hstoreType)\n            options.hstoreType =\n                reflectMetadataType === Object ? \"object\" : \"string\"\n\n        // if we still don't have a type then we need to give error to user that type is required\n        if (!options.type)\n            throw new ColumnTypeUndefinedError(object, propertyName)\n\n        getMetadataArgsStorage().columns.push({\n            target: object.constructor,\n            propertyName: propertyName,\n            mode: \"virtual-property\",\n            options: options || {},\n        } as ColumnMetadataArgs)\n    }\n}\n"], "sourceRoot": "../.."}