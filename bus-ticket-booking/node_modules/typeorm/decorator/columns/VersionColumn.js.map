{"version": 3, "sources": ["../../src/decorator/columns/VersionColumn.ts"], "names": [], "mappings": ";;AASA,sCASC;AAlBD,2CAAsD;AAItD;;;;GAIG;AACH,SAAgB,aAAa,CAAC,OAAuB;IACjD,OAAO,UAAU,MAAc,EAAE,YAAoB;QACjD,IAAA,gCAAsB,GAAE,CAAC,OAAO,CAAC,IAAI,CAAC;YAClC,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,YAAY,EAAE,YAAY;YAC1B,IAAI,EAAE,SAAS;YACf,OAAO,EAAE,OAAO,IAAI,EAAE;SACH,CAAC,CAAA;IAC5B,CAAC,CAAA;AACL,CAAC", "file": "VersionColumn.js", "sourcesContent": ["import { getMetadataArgsStorage } from \"../../globals\"\nimport { ColumnMetadataArgs } from \"../../metadata-args/ColumnMetadataArgs\"\nimport { ColumnOptions } from \"../options/ColumnOptions\"\n\n/**\n * This column will store a number - version of the entity.\n * Every time your entity will be persisted, this number will be increased by one -\n * so you can organize visioning and update strategies of your entity.\n */\nexport function VersionColumn(options?: ColumnOptions): PropertyDecorator {\n    return function (object: Object, propertyName: string) {\n        getMetadataArgsStorage().columns.push({\n            target: object.constructor,\n            propertyName: propertyName,\n            mode: \"version\",\n            options: options || {},\n        } as ColumnMetadataArgs)\n    }\n}\n"], "sourceRoot": "../.."}