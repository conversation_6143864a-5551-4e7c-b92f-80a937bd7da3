import { EntityOptions } from "../options/EntityOptions";
/**
 * This decorator is used to mark classes that will be an entity (table or document depend on database type).
 * Database schema will be created for all classes decorated with it, and Repository can be retrieved and used for it.
 */
export declare function Entity(options?: EntityOptions): ClassDecorator;
/**
 * This decorator is used to mark classes that will be an entity (table or document depend on database type).
 * Database schema will be created for all classes decorated with it, and Repository can be retrieved and used for it.
 */
export declare function Entity(name?: string, options?: EntityOptions): ClassDecorator;
