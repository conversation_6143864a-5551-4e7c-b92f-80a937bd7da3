import { JoinColumnOptions } from "../options/JoinColumnOptions";
/**
 * JoinColumn decorator used on one-to-one relations to specify owner side of relationship.
 * It also can be used on both one-to-one and many-to-one relations to specify custom column name
 * or custom referenced column.
 */
export declare function JoinColumn(): PropertyDecorator;
/**
 * JoinColumn decorator used on one-to-one relations to specify owner side of relationship.
 * It also can be used on both one-to-one and many-to-one relations to specify custom column name
 * or custom referenced column.
 */
export declare function JoinColumn(options: JoinColumnOptions): PropertyDecorator;
/**
 * JoinColumn decorator used on one-to-one relations to specify owner side of relationship.
 * It also can be used on both one-to-one and many-to-one relations to specify custom column name
 * or custom referenced column.
 */
export declare function JoinColumn(options: JoinColumnOptions[]): PropertyDecorator;
