{"version": 3, "sources": ["../../src/error/UsingJoinTableOnlyOnOneSideAllowedError.ts"], "names": [], "mappings": ";;;AAEA,iDAA6C;AAE7C,MAAa,uCAAwC,SAAQ,2BAAY;IACrE,YAAY,cAA8B,EAAE,QAA0B;QAClE,KAAK,CACD,gFAAgF;YAC5E,QAAQ,cAAc,CAAC,IAAI,IAAI,QAAQ,CAAC,YAAY,QAChD,QAAQ,CAAC,qBAAqB,CAAC,IACnC,IAAI,QAAQ,CAAC,eAAgB,CAAC,YAAY,GAAG;YAC7C,wFAAwF,CAC/F,CAAA;IACL,CAAC;CACJ;AAVD,0FAUC", "file": "UsingJoinTableOnlyOnOneSideAllowedError.js", "sourcesContent": ["import { EntityMetadata } from \"../metadata/EntityMetadata\"\nimport { RelationMetadata } from \"../metadata/RelationMetadata\"\nimport { TypeORMError } from \"./TypeORMError\"\n\nexport class UsingJoinTableOnlyOnOneSideAllowedError extends TypeORMError {\n    constructor(entityMetadata: EntityMetadata, relation: RelationMetadata) {\n        super(\n            `Using JoinTable is allowed only on one side of the many-to-many relationship. ` +\n                `Both ${entityMetadata.name}#${relation.propertyName} and ${\n                    relation.inverseEntityMetadata.name\n                }#${relation.inverseRelation!.propertyName} ` +\n                `has JoinTable decorators. Choose one of them and left JoinColumn decorator only on it.`,\n        )\n    }\n}\n"], "sourceRoot": ".."}