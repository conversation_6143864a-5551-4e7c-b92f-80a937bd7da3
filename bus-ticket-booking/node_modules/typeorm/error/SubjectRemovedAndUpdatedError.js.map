{"version": 3, "sources": ["../../src/error/SubjectRemovedAndUpdatedError.ts"], "names": [], "mappings": ";;;AACA,iDAA6C;AAE7C;;GAEG;AACH,MAAa,6BAA8B,SAAQ,2BAAY;IAC3D,YAAY,OAAgB;QACxB,KAAK,CACD,mBAAmB,OAAO,CAAC,QAAQ,CAAC,IAAI,4CAA4C;YAChF,6HAA6H,CACpI,CAAA;IACL,CAAC;CACJ;AAPD,sEAOC", "file": "SubjectRemovedAndUpdatedError.js", "sourcesContent": ["import { Subject } from \"../persistence/Subject\"\nimport { TypeORMError } from \"./TypeORMError\"\n\n/**\n * Thrown when same object is scheduled for remove and updation at the same time.\n */\nexport class SubjectRemovedAndUpdatedError extends TypeORMError {\n    constructor(subject: Subject) {\n        super(\n            `Removed entity \"${subject.metadata.name}\" is also scheduled for update operation. ` +\n                `Make sure you are not updating and removing same object (note that update or remove may be executed by cascade operations).`,\n        )\n    }\n}\n"], "sourceRoot": ".."}