{"version": 3, "sources": ["../../src/metadata/UniqueMetadata.ts"], "names": [], "mappings": ";;;AAKA,oCAAuC;AAGvC;;GAEG;AACH,MAAa,cAAc;IAuDvB,wEAAwE;IACxE,cAAc;IACd,wEAAwE;IAExE,YAAY,OAKX;QA5CD;;WAEG;QACH,YAAO,GAAqB,EAAE,CAAA;QA0B9B;;;WAGG;QACH,+BAA0B,GAA8B,EAAE,CAAA;QAYtD,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAA;QAC5C,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAA;QAChD,IAAI,OAAO,CAAC,OAAO;YAAE,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC,OAAO,CAAA;QAEnD,IAAI,OAAO,CAAC,IAAI,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAA;YACjC,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAA;YAClC,IAAI,CAAC,gBAAgB,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAA;YAC5C,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,UAAU,CAAA;QAC7C,CAAC;IACL,CAAC;IAED,wEAAwE;IACxE,uBAAuB;IACvB,wEAAwE;IAExE;;;OAGG;IACH,KAAK,CAAC,cAAuC;QACzC,MAAM,GAAG,GAA8B,EAAE,CAAA;QAEzC,8DAA8D;QAC9D,IAAI,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACxB,IAAI,mBAAmB,GAAa,EAAE,CAAA;YACtC,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC;gBACvC,mBAAmB,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAC3C,CAAC,UAAU,EAAE,EAAE;oBACX,IAAI,IAAI,CAAC,gBAAgB;wBACrB,OAAO,CACH,IAAI,CAAC,gBAAgB,CAAC,YAAY;4BAClC,GAAG;4BACH,UAAU,CACb,CAAA;oBAEL,OAAO,UAAU,CAAC,IAAI,EAAE,CAAA;gBAC5B,CAAC,CACJ,CAAA;gBACD,mBAAmB,CAAC,OAAO,CACvB,CAAC,YAAY,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,CAC5C,CAAA;YACL,CAAC;iBAAM,CAAC;gBACJ,2GAA2G;gBAC3G,MAAM,eAAe,GAAG,IAAI,CAAC,gBAAgB,CACzC,IAAI,CAAC,cAAc,CAAC,aAAa,CACpC,CAAA;gBACD,IAAI,KAAK,CAAC,OAAO,CAAC,eAAe,CAAC,EAAE,CAAC;oBACjC,mBAAmB,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,CAAM,EAAE,EAAE,CACjD,MAAM,CAAC,CAAC,CAAC,CACZ,CAAA;oBACD,mBAAmB,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;gBAC1D,CAAC;qBAAM,CAAC;oBACJ,mBAAmB,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,CAClD,CAAC,CAAM,EAAE,EAAE,CAAC,MAAM,CAAC,CAAC,CAAC,CACxB,CAAA;oBACD,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,OAAO,CAChC,CAAC,UAAU,EAAE,EAAE,CACX,CAAC,GAAG,CAAC,UAAU,CAAC,GAAG,eAAe,CAAC,UAAU,CAAC,CAAC,CACtD,CAAA;gBACL,CAAC;YACL,CAAC;YAED,IAAI,CAAC,OAAO,GAAG,mBAAmB;iBAC7B,GAAG,CAAC,CAAC,YAAY,EAAE,EAAE;gBAClB,MAAM,kBAAkB,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CACvD,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,YAAY,KAAK,YAAY,CACnD,CAAA;gBACD,IAAI,kBAAkB,EAAE,CAAC;oBACrB,OAAO,CAAC,kBAAkB,CAAC,CAAA;gBAC/B,CAAC;gBACD,MAAM,oBAAoB,GACtB,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,IAAI,CAC9B,CAAC,QAAQ,EAAE,EAAE,CACT,QAAQ,CAAC,gBAAgB;oBACzB,QAAQ,CAAC,YAAY,KAAK,YAAY,CAC7C,CAAA;gBACL,IAAI,oBAAoB,EAAE,CAAC;oBACvB,OAAO,oBAAoB,CAAC,WAAW,CAAA;gBAC3C,CAAC;gBACD,MAAM,SAAS,GAAG,IAAI,CAAC,SAAS;oBAC5B,CAAC,CAAC,GAAG,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI;oBAC7B,CAAC,CAAC,EAAE,CAAA;gBACR,MAAM,UAAU,GAAG,IAAI,CAAC,cAAc,CAAC,UAAU,CAAA;gBACjD,MAAM,IAAI,oBAAY,CAClB,qBAAqB,SAAS,kDAAkD,UAAU,KAAK;oBAC3F,YAAY,CACnB,CAAA;YACL,CAAC,CAAC;iBACD,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAA;QACtC,CAAC;QAED,IAAI,CAAC,0BAA0B,GAAG,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,MAAM,CACrD,CAAC,UAAU,EAAE,GAAG,EAAE,EAAE;YAChB,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,CAAC,IAAI,CAC3C,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,YAAY,KAAK,GAAG,CAC1C,CAAA;YACD,IAAI,MAAM;gBAAE,UAAU,CAAC,MAAM,CAAC,YAAY,CAAC,GAAG,GAAG,CAAC,GAAG,CAAC,CAAA;YAEtD,OAAO,UAAU,CAAA;QACrB,CAAC,EACD,EAA+B,CAClC,CAAA;QAED,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,SAAS;YACtB,CAAC,CAAC,IAAI,CAAC,SAAS;YAChB,CAAC,CAAC,cAAc,CAAC,oBAAoB,CAC/B,IAAI,CAAC,cAAc,CAAC,SAAS,EAC7B,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,YAAY,CAAC,CACpD,CAAA;QACP,OAAO,IAAI,CAAA;IACf,CAAC;CACJ;AAjLD,wCAiLC", "file": "UniqueMetadata.js", "sourcesContent": ["import { EmbeddedMetadata } from \"./EmbeddedMetadata\"\nimport { EntityMetadata } from \"./EntityMetadata\"\nimport { NamingStrategyInterface } from \"../naming-strategy/NamingStrategyInterface\"\nimport { ColumnMetadata } from \"./ColumnMetadata\"\nimport { UniqueMetadataArgs } from \"../metadata-args/UniqueMetadataArgs\"\nimport { TypeORMError } from \"../error\"\nimport { DeferrableType } from \"./types/DeferrableType\"\n\n/**\n * Unique metadata contains all information about table's unique constraints.\n */\nexport class UniqueMetadata {\n    // ---------------------------------------------------------------------\n    // Public Properties\n    // ---------------------------------------------------------------------\n\n    /**\n     * Entity metadata of the class to which this unique constraint is applied.\n     */\n    entityMetadata: EntityMetadata\n\n    /**\n     * Embedded metadata if this unique was applied on embedded.\n     */\n    embeddedMetadata?: EmbeddedMetadata\n\n    /**\n     * Target class to which metadata is applied.\n     */\n    target?: Function | string\n\n    /**\n     * Unique columns.\n     */\n    columns: ColumnMetadata[] = []\n\n    /**\n     * Indicate if unique constraints can be deferred.\n     */\n    deferrable?: DeferrableType\n\n    /**\n     * User specified unique constraint name.\n     */\n    givenName?: string\n\n    /**\n     * User specified column names.\n     */\n    givenColumnNames?:\n        | ((object?: any) => any[] | { [key: string]: number })\n        | string[]\n\n    /**\n     * Final unique constraint name.\n     * If unique constraint name was given by a user then it stores normalized (by naming strategy) givenName.\n     * If unique constraint name was not given then its generated.\n     */\n    name: string\n\n    /**\n     * Map of column names with order set.\n     * Used only by MongoDB driver.\n     */\n    columnNamesWithOrderingMap: { [key: string]: number } = {}\n\n    // ---------------------------------------------------------------------\n    // Constructor\n    // ---------------------------------------------------------------------\n\n    constructor(options: {\n        entityMetadata: EntityMetadata\n        embeddedMetadata?: EmbeddedMetadata\n        columns?: ColumnMetadata[]\n        args?: UniqueMetadataArgs\n    }) {\n        this.entityMetadata = options.entityMetadata\n        this.embeddedMetadata = options.embeddedMetadata\n        if (options.columns) this.columns = options.columns\n\n        if (options.args) {\n            this.target = options.args.target\n            this.givenName = options.args.name\n            this.givenColumnNames = options.args.columns\n            this.deferrable = options.args.deferrable\n        }\n    }\n\n    // ---------------------------------------------------------------------\n    // Public Build Methods\n    // ---------------------------------------------------------------------\n\n    /**\n     * Builds some depend unique constraint properties.\n     * Must be called after all entity metadata's properties map, columns and relations are built.\n     */\n    build(namingStrategy: NamingStrategyInterface): this {\n        const map: { [key: string]: number } = {}\n\n        // if columns already an array of string then simply return it\n        if (this.givenColumnNames) {\n            let columnPropertyPaths: string[] = []\n            if (Array.isArray(this.givenColumnNames)) {\n                columnPropertyPaths = this.givenColumnNames.map(\n                    (columnName) => {\n                        if (this.embeddedMetadata)\n                            return (\n                                this.embeddedMetadata.propertyPath +\n                                \".\" +\n                                columnName\n                            )\n\n                        return columnName.trim()\n                    },\n                )\n                columnPropertyPaths.forEach(\n                    (propertyPath) => (map[propertyPath] = 1),\n                )\n            } else {\n                // if columns is a function that returns array of field names then execute it and get columns names from it\n                const columnsFnResult = this.givenColumnNames(\n                    this.entityMetadata.propertiesMap,\n                )\n                if (Array.isArray(columnsFnResult)) {\n                    columnPropertyPaths = columnsFnResult.map((i: any) =>\n                        String(i),\n                    )\n                    columnPropertyPaths.forEach((name) => (map[name] = 1))\n                } else {\n                    columnPropertyPaths = Object.keys(columnsFnResult).map(\n                        (i: any) => String(i),\n                    )\n                    Object.keys(columnsFnResult).forEach(\n                        (columnName) =>\n                            (map[columnName] = columnsFnResult[columnName]),\n                    )\n                }\n            }\n\n            this.columns = columnPropertyPaths\n                .map((propertyName) => {\n                    const columnWithSameName = this.entityMetadata.columns.find(\n                        (column) => column.propertyPath === propertyName,\n                    )\n                    if (columnWithSameName) {\n                        return [columnWithSameName]\n                    }\n                    const relationWithSameName =\n                        this.entityMetadata.relations.find(\n                            (relation) =>\n                                relation.isWithJoinColumn &&\n                                relation.propertyName === propertyName,\n                        )\n                    if (relationWithSameName) {\n                        return relationWithSameName.joinColumns\n                    }\n                    const indexName = this.givenName\n                        ? '\"' + this.givenName + '\" '\n                        : \"\"\n                    const entityName = this.entityMetadata.targetName\n                    throw new TypeORMError(\n                        `Unique constraint ${indexName}contains column that is missing in the entity (${entityName}): ` +\n                            propertyName,\n                    )\n                })\n                .reduce((a, b) => a.concat(b))\n        }\n\n        this.columnNamesWithOrderingMap = Object.keys(map).reduce(\n            (updatedMap, key) => {\n                const column = this.entityMetadata.columns.find(\n                    (column) => column.propertyPath === key,\n                )\n                if (column) updatedMap[column.databasePath] = map[key]\n\n                return updatedMap\n            },\n            {} as { [key: string]: number },\n        )\n\n        this.name = this.givenName\n            ? this.givenName\n            : namingStrategy.uniqueConstraintName(\n                  this.entityMetadata.tableName,\n                  this.columns.map((column) => column.databaseName),\n              )\n        return this\n    }\n}\n"], "sourceRoot": ".."}