{"version": 3, "sources": ["../../src/commands/VersionCommand.ts"], "names": [], "mappings": ";;;AACA,iDAAoC;AAEpC;;GAEG;AACH,MAAa,cAAc;IAA3B;QACI,YAAO,GAAG,SAAS,CAAA;QACnB,aAAQ,GAAG,2CAA2C,CAAA;IAwD1D,CAAC;IAtDG,KAAK,CAAC,OAAO;QACT,MAAM,YAAY,GAAG,MAAM,cAAc,CAAC,cAAc,CACpD,oBAAoB,CACvB,CAAA;QACD,MAAM,YAAY,GAAG,YAAY,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAA;QAC1D,MAAM,eAAe,GAAG,CACpB,YAAY,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CACzD;aACI,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;aAC1B,IAAI,EAAE,CAAA;QAEX,MAAM,aAAa,GAAG,MAAM,cAAc,CAAC,cAAc,CACrD,uBAAuB,CAC1B,CAAA;QACD,MAAM,aAAa,GAAG,aAAa,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAA;QAC5D,MAAM,gBAAgB,GAAG,CACrB,aAAa,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAC5D;aACI,OAAO,CAAC,aAAa,EAAE,EAAE,CAAC;aAC1B,IAAI,EAAE,CAAA;QAEX,IAAI,eAAe,EAAE,CAAC;YAClB,OAAO,CAAC,GAAG,CAAC,0BAA0B,EAAE,eAAe,CAAC,CAAA;QAC5D,CAAC;aAAM,CAAC;YACJ,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAA;QACxD,CAAC;QACD,IAAI,gBAAgB,EAAE,CAAC;YACnB,OAAO,CAAC,GAAG,CAAC,mCAAmC,EAAE,gBAAgB,CAAC,CAAA;QACtE,CAAC;aAAM,CAAC;YACJ,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAA;QACjD,CAAC;QAED,IACI,eAAe;YACf,gBAAgB;YAChB,eAAe,KAAK,gBAAgB,EACtC,CAAC;YACC,OAAO,CAAC,GAAG,CACP,0FAA0F;gBACtF,mEAAmE,CAC1E,CAAA;QACL,CAAC;IACL,CAAC;IAES,MAAM,CAAC,cAAc,CAAC,OAAe;QAC3C,OAAO,IAAI,OAAO,CAAS,CAAC,EAAE,EAAE,IAAI,EAAE,EAAE;YACpC,IAAA,oBAAI,EAAC,OAAO,EAAE,CAAC,KAAU,EAAE,MAAW,EAAE,MAAW,EAAE,EAAE;gBACnD,IAAI,MAAM;oBAAE,OAAO,EAAE,CAAC,MAAM,CAAC,CAAA;gBAC7B,IAAI,MAAM;oBAAE,OAAO,EAAE,CAAC,MAAM,CAAC,CAAA;gBAC7B,IAAI,KAAK;oBAAE,OAAO,IAAI,CAAC,KAAK,CAAC,CAAA;gBAC7B,EAAE,CAAC,EAAE,CAAC,CAAA;YACV,CAAC,CAAC,CAAA;QACN,CAAC,CAAC,CAAA;IACN,CAAC;CACJ;AA1DD,wCA0DC", "file": "VersionCommand.js", "sourcesContent": ["import * as yargs from \"yargs\"\nimport { exec } from \"child_process\"\n\n/**\n * Shows typeorm version.\n */\nexport class VersionCommand implements yargs.CommandModule {\n    command = \"version\"\n    describe = \"Prints TypeORM version this project uses.\"\n\n    async handler() {\n        const localNpmList = await VersionCommand.executeCommand(\n            \"npm list --depth=0\",\n        )\n        const localMatches = localNpmList.match(/ typeorm@(.*)\\n/)\n        const localNpmVersion = (\n            localMatches && localMatches[1] ? localMatches[1] : \"\"\n        )\n            .replace(/\"invalid\"/gi, \"\")\n            .trim()\n\n        const globalNpmList = await VersionCommand.executeCommand(\n            \"npm list -g --depth=0\",\n        )\n        const globalMatches = globalNpmList.match(/ typeorm@(.*)\\n/)\n        const globalNpmVersion = (\n            globalMatches && globalMatches[1] ? globalMatches[1] : \"\"\n        )\n            .replace(/\"invalid\"/gi, \"\")\n            .trim()\n\n        if (localNpmVersion) {\n            console.log(\"Local installed version:\", localNpmVersion)\n        } else {\n            console.log(\"No local installed TypeORM was found.\")\n        }\n        if (globalNpmVersion) {\n            console.log(\"Global installed TypeORM version:\", globalNpmVersion)\n        } else {\n            console.log(\"No global installed was found.\")\n        }\n\n        if (\n            localNpmVersion &&\n            globalNpmVersion &&\n            localNpmVersion !== globalNpmVersion\n        ) {\n            console.log(\n                \"To avoid issues with CLI please make sure your global and local TypeORM versions match, \" +\n                    \"or you are using locally installed TypeORM instead of global one.\",\n            )\n        }\n    }\n\n    protected static executeCommand(command: string) {\n        return new Promise<string>((ok, fail) => {\n            exec(command, (error: any, stdout: any, stderr: any) => {\n                if (stdout) return ok(stdout)\n                if (stderr) return ok(stderr)\n                if (error) return fail(error)\n                ok(\"\")\n            })\n        })\n    }\n}\n"], "sourceRoot": ".."}