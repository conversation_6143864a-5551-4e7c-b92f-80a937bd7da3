# Application
NODE_ENV=development
PORT=3000
APP_NAME=Bus Ticket Booking API

# Database
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=password
DB_NAME=bus_ticket_booking

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h

# OpenWeatherMap API
OPENWEATHER_API_KEY=your-openweather-api-key
OPENWEATHER_BASE_URL=https://api.openweathermap.org/data/2.5

# Rate Limiting
THROTTLE_TTL=60
THROTTLE_LIMIT=10

# Swagger
SWAGGER_TITLE=Bus Ticket Booking API
SWAGGER_DESCRIPTION=A comprehensive bus ticket booking system API
SWAGGER_VERSION=1.0
SWAGGER_PATH=api/docs
