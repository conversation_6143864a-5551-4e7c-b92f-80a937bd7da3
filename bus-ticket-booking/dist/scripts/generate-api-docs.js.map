{"version": 3, "file": "generate-api-docs.js", "sourceRoot": "", "sources": ["../../scripts/generate-api-docs.ts"], "names": [], "mappings": ";;AAAA,uCAA2C;AAC3C,6CAAiE;AACjE,2BAA8C;AAC9C,+BAA4B;AAC5B,kDAA8C;AAE9C,KAAK,UAAU,eAAe;IAC5B,MAAM,GAAG,GAAG,MAAM,kBAAW,CAAC,MAAM,CAAC,sBAAS,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;IAEnE,MAAM,MAAM,GAAG,IAAI,yBAAe,EAAE;SACjC,QAAQ,CAAC,wBAAwB,CAAC;SAClC,cAAc,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;KA6Cf,CAAC;SACD,UAAU,CAAC,OAAO,CAAC;SACnB,aAAa,EAAE;SACf,SAAS,CAAC,uBAAuB,EAAE,oBAAoB,CAAC;SACxD,SAAS,CAAC,6BAA6B,EAAE,mBAAmB,CAAC;SAC7D,MAAM,CAAC,gBAAgB,EAAE,uCAAuC,CAAC;SACjE,MAAM,CAAC,OAAO,EAAE,4BAA4B,CAAC;SAC7C,MAAM,CAAC,SAAS,EAAE,+BAA+B,CAAC;SAClD,MAAM,CAAC,SAAS,EAAE,gCAAgC,CAAC;SACnD,MAAM,CAAC,iBAAiB,EAAE,gCAAgC,CAAC;SAC3D,UAAU,CAAC,yBAAyB,EAAE,iCAAiC,EAAE,yBAAyB,CAAC;SACnG,UAAU,CAAC,KAAK,EAAE,qCAAqC,CAAC;SACxD,KAAK,EAAE,CAAC;IAEX,MAAM,QAAQ,GAAG,uBAAa,CAAC,cAAc,CAAC,GAAG,EAAE,MAAM,CAAC,CAAC;IAG3D,MAAM,OAAO,GAAG,IAAA,WAAI,EAAC,OAAO,CAAC,GAAG,EAAE,EAAE,MAAM,CAAC,CAAC;IAC5C,IAAI,CAAC;QACH,IAAA,cAAS,EAAC,OAAO,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;IAC1C,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;IAEjB,CAAC;IAGD,MAAM,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IACtD,IAAA,kBAAa,EAAC,IAAA,WAAI,EAAC,OAAO,EAAE,cAAc,CAAC,EAAE,WAAW,CAAC,CAAC;IAG1D,MAAM,IAAI,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC;IAChC,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;IACxC,IAAA,kBAAa,EAAC,IAAA,WAAI,EAAC,OAAO,EAAE,cAAc,CAAC,EAAE,WAAW,CAAC,CAAC;IAG1D,MAAM,YAAY,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4CpB,CAAC;IAEF,IAAA,kBAAa,EAAC,IAAA,WAAI,EAAC,OAAO,EAAE,YAAY,CAAC,EAAE,YAAY,CAAC,CAAC;IAGzD,MAAM,iBAAiB,GAAG;QACxB,IAAI,EAAE;YACJ,IAAI,EAAE,wBAAwB;YAC9B,WAAW,EAAE,8CAA8C;YAC3D,OAAO,EAAE,OAAO;YAChB,MAAM,EAAE,sEAAsE;SAC/E;QACD,IAAI,EAAE;YACJ,IAAI,EAAE,QAAQ;YACd,MAAM,EAAE;gBACN;oBACE,GAAG,EAAE,OAAO;oBACZ,KAAK,EAAE,eAAe;oBACtB,IAAI,EAAE,QAAQ;iBACf;aACF;SACF;QACD,QAAQ,EAAE;YACR;gBACE,GAAG,EAAE,UAAU;gBACf,KAAK,EAAE,uBAAuB;gBAC9B,IAAI,EAAE,QAAQ;aACf;YACD;gBACE,GAAG,EAAE,WAAW;gBAChB,KAAK,EAAE,EAAE;gBACT,IAAI,EAAE,QAAQ;aACf;SACF;QACD,IAAI,EAAE,EAAE;KACT,CAAC;IAEF,IAAA,kBAAa,EAAC,IAAA,WAAI,EAAC,OAAO,EAAE,yBAAyB,CAAC,EAAE,IAAI,CAAC,SAAS,CAAC,iBAAiB,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC;IAEpG,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;IAC3D,OAAO,CAAC,GAAG,CAAC,mBAAmB,CAAC,CAAC;IACjC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;IACrC,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;IACrC,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;IACnC,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;IAChD,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC;IAChB,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;IAC7C,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;IACzD,OAAO,CAAC,GAAG,CAAC,8DAA8D,CAAC,CAAC;IAE5E,MAAM,GAAG,CAAC,KAAK,EAAE,CAAC;AACpB,CAAC;AAED,eAAe,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC"}