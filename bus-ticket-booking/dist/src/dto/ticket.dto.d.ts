import { TicketStatus } from '../entities';
export declare class CreateTicketDto {
    tripId: string;
    seatId: string;
}
export declare class UpdateTicketDto {
    status?: TicketStatus;
}
export declare class TicketResponseDto {
    id: string;
    userId: string;
    tripId: string;
    seatId: string;
    bookingDate: Date;
    status: TicketStatus;
    createdAt: Date;
    updatedAt: Date;
    trip?: {
        id: string;
        departure: string;
        destination: string;
        departureTime: Date;
        arrivalTime: Date;
        price: number;
    };
    seat?: {
        id: string;
        seatNumber: string;
    };
}
