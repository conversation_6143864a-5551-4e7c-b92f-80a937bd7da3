export declare class CreateTripDto {
    departure: string;
    destination: string;
    departureTime: string;
    arrivalTime: string;
    price: number;
    totalSeats: number;
    driverId: string;
}
export declare class UpdateTripDto {
    departure?: string;
    destination?: string;
    departureTime?: string;
    arrivalTime?: string;
    price?: number;
    driverId?: string;
}
export declare class SearchTripsDto {
    departure: string;
    destination: string;
    date: string;
    page?: number;
    limit?: number;
}
export declare class TripResponseDto {
    id: string;
    departure: string;
    destination: string;
    departureTime: Date;
    arrivalTime: Date;
    price: number;
    totalSeats: number;
    availableSeats: number;
    driverId: string;
    createdAt: Date;
    updatedAt: Date;
}
