"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WeatherResponseDto = exports.WeatherQueryDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class WeatherQueryDto {
    city;
}
exports.WeatherQueryDto = WeatherQueryDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        example: 'New York',
        description: 'City name for weather information'
    }),
    (0, class_validator_1.IsString)(),
    (0, class_validator_1.IsNotEmpty)(),
    (0, class_validator_1.Matches)(/^[a-zA-Z\s\-']+$/, {
        message: 'City name can only contain letters, spaces, hyphens, and apostrophes'
    }),
    __metadata("design:type", String)
], WeatherQueryDto.prototype, "city", void 0);
class WeatherResponseDto {
    city;
    country;
    temperature;
    feelsLike;
    description;
    main;
    humidity;
    pressure;
    windSpeed;
    windDirection;
    visibility;
    timestamp;
    source;
}
exports.WeatherResponseDto = WeatherResponseDto;
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'New York' }),
    __metadata("design:type", String)
], WeatherResponseDto.prototype, "city", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'US' }),
    __metadata("design:type", String)
], WeatherResponseDto.prototype, "country", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 22.5 }),
    __metadata("design:type", Number)
], WeatherResponseDto.prototype, "temperature", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Feels like 25°C' }),
    __metadata("design:type", String)
], WeatherResponseDto.prototype, "feelsLike", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'Clear sky' }),
    __metadata("design:type", String)
], WeatherResponseDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'clear' }),
    __metadata("design:type", String)
], WeatherResponseDto.prototype, "main", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 65 }),
    __metadata("design:type", Number)
], WeatherResponseDto.prototype, "humidity", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 1013 }),
    __metadata("design:type", Number)
], WeatherResponseDto.prototype, "pressure", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 5.2 }),
    __metadata("design:type", Number)
], WeatherResponseDto.prototype, "windSpeed", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'SW' }),
    __metadata("design:type", String)
], WeatherResponseDto.prototype, "windDirection", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 10 }),
    __metadata("design:type", Number)
], WeatherResponseDto.prototype, "visibility", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: '2024-01-15T10:30:00Z' }),
    __metadata("design:type", String)
], WeatherResponseDto.prototype, "timestamp", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({ example: 'cached' }),
    __metadata("design:type", String)
], WeatherResponseDto.prototype, "source", void 0);
//# sourceMappingURL=weather.dto.js.map