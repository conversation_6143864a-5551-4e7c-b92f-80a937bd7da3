{"version": 3, "file": "trip.dto.js", "sourceRoot": "", "sources": ["../../../src/dto/trip.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAAiG;AACjG,6CAAmE;AACnE,yDAAyC;AAEzC,MAAa,aAAa;IAGxB,SAAS,CAAS;IAIlB,WAAW,CAAS;IAIpB,aAAa,CAAS;IAItB,WAAW,CAAS;IAKpB,KAAK,CAAS;IAMd,UAAU,CAAS;IAInB,QAAQ,CAAS;CAClB;AA/BD,sCA+BC;AA5BC;IAFC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;IACpC,IAAA,0BAAQ,GAAE;;gDACO;AAIlB;IAFC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;IAClC,IAAA,0BAAQ,GAAE;;kDACS;AAIpB;IAFC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;IAChD,IAAA,8BAAY,GAAE;;oDACO;AAItB;IAFC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;IAChD,IAAA,8BAAY,GAAE;;kDACK;AAKpB;IAHC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;IAC/B,IAAA,0BAAQ,EAAC,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;IACjC,IAAA,qBAAG,EAAC,IAAI,CAAC;;4CACI;AAMd;IAJC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;IAC5B,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;IACN,IAAA,qBAAG,EAAC,GAAG,CAAC;;iDACU;AAInB;IAFC,IAAA,qBAAW,GAAE;IACb,IAAA,wBAAM,GAAE;;+CACQ;AAGnB,MAAa,aAAa;IAIxB,SAAS,CAAU;IAKnB,WAAW,CAAU;IAKrB,aAAa,CAAU;IAKvB,WAAW,CAAU;IAMrB,KAAK,CAAU;IAKf,QAAQ,CAAU;CACnB;AA/BD,sCA+BC;AA3BC;IAHC,IAAA,6BAAmB,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;IAC5C,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;gDACQ;AAKnB;IAHC,IAAA,6BAAmB,EAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;IAC1C,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;kDACU;AAKrB;IAHC,IAAA,6BAAmB,EAAC,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;IACxD,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,GAAE;;oDACQ;AAKvB;IAHC,IAAA,6BAAmB,EAAC,EAAE,OAAO,EAAE,sBAAsB,EAAE,CAAC;IACxD,IAAA,4BAAU,GAAE;IACZ,IAAA,8BAAY,GAAE;;kDACM;AAMrB;IAJC,IAAA,6BAAmB,EAAC,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC;IACvC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,EAAC,EAAE,gBAAgB,EAAE,CAAC,EAAE,CAAC;IACjC,IAAA,qBAAG,EAAC,IAAI,CAAC;;4CACK;AAKf;IAHC,IAAA,6BAAmB,GAAE;IACrB,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAM,GAAE;;+CACS;AAGpB,MAAa,cAAc;IAGzB,SAAS,CAAS;IAIlB,WAAW,CAAS;IAIpB,IAAI,CAAS;IAOb,IAAI,GAAY,CAAC,CAAC;IAQlB,KAAK,GAAY,EAAE,CAAC;CACrB;AA3BD,wCA2BC;AAxBC;IAFC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,UAAU,EAAE,CAAC;IACpC,IAAA,0BAAQ,GAAE;;iDACO;AAIlB;IAFC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;IAClC,IAAA,0BAAQ,GAAE;;mDACS;AAIpB;IAFC,IAAA,qBAAW,EAAC,EAAE,OAAO,EAAE,YAAY,EAAE,CAAC;IACtC,IAAA,8BAAY,GAAE;;4CACF;AAOb;IALC,IAAA,6BAAmB,EAAC,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IAC/C,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;;4CACW;AAQlB;IANC,IAAA,6BAAmB,EAAC,EAAE,OAAO,EAAE,EAAE,EAAE,OAAO,EAAE,EAAE,EAAE,CAAC;IACjD,IAAA,4BAAU,GAAE;IACZ,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,MAAM,CAAC;IAClB,IAAA,0BAAQ,GAAE;IACV,IAAA,qBAAG,EAAC,CAAC,CAAC;IACN,IAAA,qBAAG,EAAC,EAAE,CAAC;;6CACY;AAGtB,MAAa,eAAe;IAE1B,EAAE,CAAS;IAGX,SAAS,CAAS;IAGlB,WAAW,CAAS;IAGpB,aAAa,CAAO;IAGpB,WAAW,CAAO;IAGlB,KAAK,CAAS;IAGd,UAAU,CAAS;IAGnB,cAAc,CAAS;IAGvB,QAAQ,CAAS;IAGjB,SAAS,CAAO;IAGhB,SAAS,CAAO;CACjB;AAjCD,0CAiCC;AA/BC;IADC,IAAA,qBAAW,GAAE;;2CACH;AAGX;IADC,IAAA,qBAAW,GAAE;;kDACI;AAGlB;IADC,IAAA,qBAAW,GAAE;;oDACM;AAGpB;IADC,IAAA,qBAAW,GAAE;8BACC,IAAI;sDAAC;AAGpB;IADC,IAAA,qBAAW,GAAE;8BACD,IAAI;oDAAC;AAGlB;IADC,IAAA,qBAAW,GAAE;;8CACA;AAGd;IADC,IAAA,qBAAW,GAAE;;mDACK;AAGnB;IADC,IAAA,qBAAW,GAAE;;uDACS;AAGvB;IADC,IAAA,qBAAW,GAAE;;iDACG;AAGjB;IADC,IAAA,qBAAW,GAAE;8BACH,IAAI;kDAAC;AAGhB;IADC,IAAA,qBAAW,GAAE;8BACH,IAAI;kDAAC"}