{"version": 3, "file": "database.config.js", "sourceRoot": "", "sources": ["../../../src/config/database.config.ts"], "names": [], "mappings": ";;AAAA,2CAA4C;AAE5C,0CAA+D;AAE/D,kBAAe,IAAA,mBAAU,EACvB,UAAU,EACV,GAAyB,EAAE,CAAC,CAAC;IAC3B,IAAI,EAAE,UAAU;IAChB,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,WAAW;IACxC,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,MAAM,EAAE,EAAE,CAAC;IACjD,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,UAAU;IAC/C,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,WAAW,IAAI,UAAU;IAC/C,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,oBAAoB;IACrD,QAAQ,EAAE,CAAC,eAAI,EAAE,iBAAM,EAAE,eAAI,EAAE,eAAI,EAAE,iBAAM,CAAC;IAC5C,UAAU,EAAE,CAAC,SAAS,GAAG,2BAA2B,CAAC;IACrD,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa;IACnD,OAAO,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,aAAa;IAC/C,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,CAAC,CAAC,CAAC,EAAE,kBAAkB,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,KAAK;IAClF,gBAAgB,EAAE,IAAI;CACvB,CAAC,CACH,CAAC"}