"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.redisConfig = exports.databaseConfig = exports.appConfig = void 0;
var app_config_1 = require("./app.config");
Object.defineProperty(exports, "appConfig", { enumerable: true, get: function () { return app_config_1.default; } });
var database_config_1 = require("./database.config");
Object.defineProperty(exports, "databaseConfig", { enumerable: true, get: function () { return database_config_1.default; } });
var redis_config_1 = require("./redis.config");
Object.defineProperty(exports, "redisConfig", { enumerable: true, get: function () { return redis_config_1.default; } });
//# sourceMappingURL=index.js.map