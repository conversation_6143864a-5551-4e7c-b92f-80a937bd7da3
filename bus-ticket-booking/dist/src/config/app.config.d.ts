declare const _default: (() => {
    nodeEnv: string;
    port: number;
    name: string;
    jwt: {
        secret: string;
        expiresIn: string;
    };
    openWeather: {
        apiKey: string | undefined;
        baseUrl: string;
    };
    throttle: {
        ttl: number;
        limit: number;
    };
    swagger: {
        title: string;
        description: string;
        version: string;
        path: string;
    };
}) & import("@nestjs/config").ConfigFactoryKeyHost<{
    nodeEnv: string;
    port: number;
    name: string;
    jwt: {
        secret: string;
        expiresIn: string;
    };
    openWeather: {
        apiKey: string | undefined;
        baseUrl: string;
    };
    throttle: {
        ttl: number;
        limit: number;
    };
    swagger: {
        title: string;
        description: string;
        version: string;
        path: string;
    };
}>;
export default _default;
