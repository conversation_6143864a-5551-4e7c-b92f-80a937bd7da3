import { Repository } from 'typeorm';
import { Driver, Trip } from '../../entities';
import { CreateDriverDto, UpdateDriverDto } from '../../dto';
export declare class DriversService {
    private readonly driverRepository;
    private readonly tripRepository;
    constructor(driverRepository: Repository<Driver>, tripRepository: Repository<Trip>);
    create(createDriverDto: CreateDriverDto): Promise<Driver>;
    findAll(page?: number, limit?: number): Promise<{
        drivers: Driver[];
        total: number;
        page: number;
        limit: number;
    }>;
    findOne(id: string): Promise<Driver>;
    update(id: string, updateDriverDto: UpdateDriverDto): Promise<Driver>;
    remove(id: string): Promise<void>;
}
