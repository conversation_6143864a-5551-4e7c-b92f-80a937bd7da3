"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DriversService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const entities_1 = require("../../entities");
let DriversService = class DriversService {
    driverRepository;
    tripRepository;
    constructor(driverRepository, tripRepository) {
        this.driverRepository = driverRepository;
        this.tripRepository = tripRepository;
    }
    async create(createDriverDto) {
        const existingDriver = await this.driverRepository.findOne({
            where: { licenseNumber: createDriverDto.licenseNumber },
        });
        if (existingDriver) {
            throw new common_1.ConflictException('Driver with this license number already exists');
        }
        const driver = this.driverRepository.create(createDriverDto);
        return this.driverRepository.save(driver);
    }
    async findAll(page = 1, limit = 10) {
        const [drivers, total] = await this.driverRepository.findAndCount({
            relations: ['trips'],
            skip: (page - 1) * limit,
            take: limit,
            order: { createdAt: 'DESC' },
        });
        return { drivers, total, page, limit };
    }
    async findOne(id) {
        const driver = await this.driverRepository.findOne({
            where: { id },
            relations: ['trips'],
        });
        if (!driver) {
            throw new common_1.NotFoundException('Driver not found');
        }
        return driver;
    }
    async update(id, updateDriverDto) {
        const driver = await this.findOne(id);
        if (updateDriverDto.licenseNumber && updateDriverDto.licenseNumber !== driver.licenseNumber) {
            const existingDriver = await this.driverRepository.findOne({
                where: { licenseNumber: updateDriverDto.licenseNumber },
            });
            if (existingDriver) {
                throw new common_1.ConflictException('Driver with this license number already exists');
            }
        }
        Object.assign(driver, updateDriverDto);
        return this.driverRepository.save(driver);
    }
    async remove(id) {
        const driver = await this.findOne(id);
        const activeTrips = await this.tripRepository.count({
            where: { driverId: id, departureTime: new Date() },
        });
        if (activeTrips > 0) {
            throw new common_1.BadRequestException('Cannot delete driver with active trips');
        }
        await this.driverRepository.remove(driver);
    }
};
exports.DriversService = DriversService;
exports.DriversService = DriversService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(entities_1.Driver)),
    __param(1, (0, typeorm_1.InjectRepository)(entities_1.Trip)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository])
], DriversService);
//# sourceMappingURL=drivers.service.js.map