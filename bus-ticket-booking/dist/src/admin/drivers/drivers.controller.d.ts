import { DriversService } from './drivers.service';
import { CreateDriverDto, UpdateDriverDto } from '../../dto';
export declare class DriversController {
    private readonly driversService;
    constructor(driversService: DriversService);
    create(createDriverDto: CreateDriverDto): Promise<import("../../entities").Driver>;
    findAll(page?: number, limit?: number): Promise<{
        drivers: import("../../entities").Driver[];
        total: number;
        page: number;
        limit: number;
    }>;
    findOne(id: string): Promise<import("../../entities").Driver>;
    update(id: string, updateDriverDto: UpdateDriverDto): Promise<import("../../entities").Driver>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
