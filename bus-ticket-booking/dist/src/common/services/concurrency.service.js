"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var ConcurrencyService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConcurrencyService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("typeorm");
let ConcurrencyService = ConcurrencyService_1 = class ConcurrencyService {
    dataSource;
    logger = new common_1.Logger(ConcurrencyService_1.name);
    constructor(dataSource) {
        this.dataSource = dataSource;
    }
    async withOptimisticRetry(operation, options = {}) {
        const { maxRetries = 3, baseDelay = 100, maxDelay = 1000, backoffFactor = 2, } = options;
        let lastError;
        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                return await this.dataSource.transaction(async (manager) => {
                    return await operation(manager);
                });
            }
            catch (error) {
                lastError = error;
                if (error instanceof typeorm_1.OptimisticLockVersionMismatchError ||
                    error.message?.includes('version') ||
                    error.message?.includes('optimistic')) {
                    if (attempt < maxRetries) {
                        const delay = Math.min(baseDelay * Math.pow(backoffFactor, attempt), maxDelay);
                        this.logger.warn(`Optimistic lock conflict on attempt ${attempt + 1}/${maxRetries + 1}. Retrying in ${delay}ms...`);
                        await this.sleep(delay);
                        continue;
                    }
                }
                throw error;
            }
        }
        throw new common_1.ConflictException(`Operation failed after ${maxRetries + 1} attempts due to concurrent modifications`);
    }
    async withPessimisticLock(operation) {
        return await this.dataSource.transaction(async (manager) => {
            return await operation(manager);
        });
    }
    async withDistributedLock(lockKey, operation, ttlSeconds = 30) {
        const lockId = `${lockKey}:${Date.now()}:${Math.random()}`;
        try {
            const acquired = await this.acquireLock(lockKey, lockId, ttlSeconds);
            if (!acquired) {
                throw new common_1.ConflictException(`Could not acquire lock for key: ${lockKey}`);
            }
            return await operation();
        }
        finally {
            await this.releaseLock(lockKey, lockId);
        }
    }
    async withCircuitBreaker(operation, options = {}) {
        const { failureThreshold = 5, resetTimeout = 60000, monitoringPeriod = 10000, } = options;
        return await operation();
    }
    async sleep(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
    async acquireLock(key, lockId, ttlSeconds) {
        return true;
    }
    async releaseLock(key, lockId) {
    }
    async withIdempotencyKey(idempotencyKey, operation, ttlSeconds = 3600) {
        const existingResult = await this.getIdempotentResult(idempotencyKey);
        if (existingResult !== null) {
            this.logger.log(`Returning cached result for idempotency key: ${idempotencyKey}`);
            return existingResult;
        }
        const result = await operation();
        await this.storeIdempotentResult(idempotencyKey, result, ttlSeconds);
        return result;
    }
    async getIdempotentResult(key) {
        return null;
    }
    async storeIdempotentResult(key, result, ttlSeconds) {
    }
    async executeSaga(steps) {
        const executedSteps = [];
        try {
            let result;
            for (const step of steps) {
                const stepResult = await step.execute();
                executedSteps.push(step.compensate);
                if (steps.indexOf(step) === steps.length - 1) {
                    result = stepResult;
                }
            }
            return result;
        }
        catch (error) {
            this.logger.error('Saga failed, executing compensation steps', error);
            for (const compensate of executedSteps.reverse()) {
                try {
                    await compensate();
                }
                catch (compensationError) {
                    this.logger.error('Compensation step failed', compensationError);
                }
            }
            throw error;
        }
    }
};
exports.ConcurrencyService = ConcurrencyService;
exports.ConcurrencyService = ConcurrencyService = ConcurrencyService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [typeorm_1.DataSource])
], ConcurrencyService);
//# sourceMappingURL=concurrency.service.js.map