"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var RedisService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const redis_1 = require("redis");
let RedisService = RedisService_1 = class RedisService {
    configService;
    logger = new common_1.Logger(RedisService_1.name);
    client;
    constructor(configService) {
        this.configService = configService;
    }
    async onModuleInit() {
        const redisConfig = this.configService.get('redis');
        this.client = (0, redis_1.createClient)({
            socket: {
                host: redisConfig.host,
                port: redisConfig.port,
            },
            password: redisConfig.password,
            database: redisConfig.db,
        });
        this.client.on('error', (err) => {
            this.logger.error('Redis Client Error', err);
        });
        this.client.on('connect', () => {
            this.logger.log('Connected to Redis');
        });
        this.client.on('disconnect', () => {
            this.logger.warn('Disconnected from Redis');
        });
        try {
            await this.client.connect();
        }
        catch (error) {
            this.logger.error('Failed to connect to Redis', error);
        }
    }
    async onModuleDestroy() {
        if (this.client) {
            await this.client.disconnect();
        }
    }
    async get(key) {
        try {
            return await this.client.get(key);
        }
        catch (error) {
            this.logger.error(`Error getting key ${key}`, error);
            return null;
        }
    }
    async set(key, value, ttlSeconds) {
        try {
            if (ttlSeconds) {
                await this.client.setEx(key, ttlSeconds, value);
            }
            else {
                await this.client.set(key, value);
            }
            return true;
        }
        catch (error) {
            this.logger.error(`Error setting key ${key}`, error);
            return false;
        }
    }
    async del(key) {
        try {
            await this.client.del(key);
            return true;
        }
        catch (error) {
            this.logger.error(`Error deleting key ${key}`, error);
            return false;
        }
    }
    async exists(key) {
        try {
            const result = await this.client.exists(key);
            return result === 1;
        }
        catch (error) {
            this.logger.error(`Error checking existence of key ${key}`, error);
            return false;
        }
    }
    async incr(key) {
        try {
            return await this.client.incr(key);
        }
        catch (error) {
            this.logger.error(`Error incrementing key ${key}`, error);
            return 0;
        }
    }
    async expire(key, seconds) {
        try {
            await this.client.expire(key, seconds);
            return true;
        }
        catch (error) {
            this.logger.error(`Error setting expiration for key ${key}`, error);
            return false;
        }
    }
    async getJson(key) {
        try {
            const value = await this.get(key);
            return value ? JSON.parse(value) : null;
        }
        catch (error) {
            this.logger.error(`Error getting JSON for key ${key}`, error);
            return null;
        }
    }
    async setJson(key, value, ttlSeconds) {
        try {
            const jsonValue = JSON.stringify(value);
            return await this.set(key, jsonValue, ttlSeconds);
        }
        catch (error) {
            this.logger.error(`Error setting JSON for key ${key}`, error);
            return false;
        }
    }
};
exports.RedisService = RedisService;
exports.RedisService = RedisService = RedisService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [config_1.ConfigService])
], RedisService);
//# sourceMappingURL=redis.service.js.map