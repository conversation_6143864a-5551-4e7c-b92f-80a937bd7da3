{"version": 3, "file": "concurrency.service.js", "sourceRoot": "", "sources": ["../../../../src/common/services/concurrency.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAuE;AACvE,qCAAwF;AAUjF,IAAM,kBAAkB,0BAAxB,MAAM,kBAAkB;IAGA;IAFZ,MAAM,GAAG,IAAI,eAAM,CAAC,oBAAkB,CAAC,IAAI,CAAC,CAAC;IAE9D,YAA6B,UAAsB;QAAtB,eAAU,GAAV,UAAU,CAAY;IAAG,CAAC;IAKvD,KAAK,CAAC,mBAAmB,CACvB,SAAiD,EACjD,UAAwB,EAAE;QAE1B,MAAM,EACJ,UAAU,GAAG,CAAC,EACd,SAAS,GAAG,GAAG,EACf,QAAQ,GAAG,IAAI,EACf,aAAa,GAAG,CAAC,GAClB,GAAG,OAAO,CAAC;QAEZ,IAAI,SAAgB,CAAC;QAErB,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,UAAU,EAAE,OAAO,EAAE,EAAE,CAAC;YACvD,IAAI,CAAC;gBACH,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;oBACzD,OAAO,MAAM,SAAS,CAAC,OAAO,CAAC,CAAC;gBAClC,CAAC,CAAC,CAAC;YACL,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,SAAS,GAAG,KAAK,CAAC;gBAGlB,IAAI,KAAK,YAAY,4CAAkC;oBACnD,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,SAAS,CAAC;oBAClC,KAAK,CAAC,OAAO,EAAE,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC;oBAE1C,IAAI,OAAO,GAAG,UAAU,EAAE,CAAC;wBACzB,MAAM,KAAK,GAAG,IAAI,CAAC,GAAG,CACpB,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,OAAO,CAAC,EAC5C,QAAQ,CACT,CAAC;wBAEF,IAAI,CAAC,MAAM,CAAC,IAAI,CACd,uCAAuC,OAAO,GAAG,CAAC,IAAI,UAAU,GAAG,CAAC,iBAAiB,KAAK,OAAO,CAClG,CAAC;wBAEF,MAAM,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;wBACxB,SAAS;oBACX,CAAC;gBACH,CAAC;gBAGD,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;QAED,MAAM,IAAI,0BAAiB,CACzB,0BAA0B,UAAU,GAAG,CAAC,2CAA2C,CACpF,CAAC;IACJ,CAAC;IAKD,KAAK,CAAC,mBAAmB,CACvB,SAAiD;QAEjD,OAAO,MAAM,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YACzD,OAAO,MAAM,SAAS,CAAC,OAAO,CAAC,CAAC;QAClC,CAAC,CAAC,CAAC;IACL,CAAC;IAKD,KAAK,CAAC,mBAAmB,CACvB,OAAe,EACf,SAA2B,EAC3B,aAAqB,EAAE;QAIvB,MAAM,MAAM,GAAG,GAAG,OAAO,IAAI,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;QAE3D,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;YACrE,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,MAAM,IAAI,0BAAiB,CAAC,mCAAmC,OAAO,EAAE,CAAC,CAAC;YAC5E,CAAC;YAED,OAAO,MAAM,SAAS,EAAE,CAAC;QAC3B,CAAC;gBAAS,CAAC;YACT,MAAM,IAAI,CAAC,WAAW,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;QAC1C,CAAC;IACH,CAAC;IAKD,KAAK,CAAC,kBAAkB,CACtB,SAA2B,EAC3B,UAII,EAAE;QAIN,MAAM,EACJ,gBAAgB,GAAG,CAAC,EACpB,YAAY,GAAG,KAAK,EACpB,gBAAgB,GAAG,KAAK,GACzB,GAAG,OAAO,CAAC;QAIZ,OAAO,MAAM,SAAS,EAAE,CAAC;IAC3B,CAAC;IAEO,KAAK,CAAC,KAAK,CAAC,EAAU;QAC5B,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;IACzD,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,GAAW,EAAE,MAAc,EAAE,UAAkB;QAGvE,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,GAAW,EAAE,MAAc;IAGrD,CAAC;IAKD,KAAK,CAAC,kBAAkB,CACtB,cAAsB,EACtB,SAA2B,EAC3B,aAAqB,IAAI;QAGzB,MAAM,cAAc,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAAI,cAAc,CAAC,CAAC;QACzE,IAAI,cAAc,KAAK,IAAI,EAAE,CAAC;YAC5B,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,gDAAgD,cAAc,EAAE,CAAC,CAAC;YAClF,OAAO,cAAc,CAAC;QACxB,CAAC;QAGD,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE,CAAC;QACjC,MAAM,IAAI,CAAC,qBAAqB,CAAC,cAAc,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;QAErE,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAAI,GAAW;QAE9C,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,KAAK,CAAC,qBAAqB,CACjC,GAAW,EACX,MAAS,EACT,UAAkB;IAGpB,CAAC;IAKD,KAAK,CAAC,WAAW,CACf,KAGE;QAEF,MAAM,aAAa,GAA+B,EAAE,CAAC;QAErD,IAAI,CAAC;YACH,IAAI,MAAS,CAAC;YAEd,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,OAAO,EAAE,CAAC;gBACxC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;gBAGpC,IAAI,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC7C,MAAM,GAAG,UAAU,CAAC;gBACtB,CAAC;YACH,CAAC;YAED,OAAO,MAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAEf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2CAA2C,EAAE,KAAK,CAAC,CAAC;YAEtE,KAAK,MAAM,UAAU,IAAI,aAAa,CAAC,OAAO,EAAE,EAAE,CAAC;gBACjD,IAAI,CAAC;oBACH,MAAM,UAAU,EAAE,CAAC;gBACrB,CAAC;gBAAC,OAAO,iBAAiB,EAAE,CAAC;oBAC3B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,iBAAiB,CAAC,CAAC;gBACnE,CAAC;YACH,CAAC;YAED,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAA;AAhNY,gDAAkB;6BAAlB,kBAAkB;IAD9B,IAAA,mBAAU,GAAE;qCAI8B,oBAAU;GAHxC,kBAAkB,CAgN9B"}