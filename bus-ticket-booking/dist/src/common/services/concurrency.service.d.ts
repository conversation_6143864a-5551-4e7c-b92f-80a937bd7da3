import { DataSource, EntityManager } from 'typeorm';
export interface RetryOptions {
    maxRetries?: number;
    baseDelay?: number;
    maxDelay?: number;
    backoffFactor?: number;
}
export declare class ConcurrencyService {
    private readonly dataSource;
    private readonly logger;
    constructor(dataSource: DataSource);
    withOptimisticRetry<T>(operation: (manager: EntityManager) => Promise<T>, options?: RetryOptions): Promise<T>;
    withPessimisticLock<T>(operation: (manager: EntityManager) => Promise<T>): Promise<T>;
    withDistributedLock<T>(lockKey: string, operation: () => Promise<T>, ttlSeconds?: number): Promise<T>;
    withCircuitBreaker<T>(operation: () => Promise<T>, options?: {
        failureThreshold?: number;
        resetTimeout?: number;
        monitoringPeriod?: number;
    }): Promise<T>;
    private sleep;
    private acquireLock;
    private releaseLock;
    withIdempotencyKey<T>(idempotencyKey: string, operation: () => Promise<T>, ttlSeconds?: number): Promise<T>;
    private getIdempotentResult;
    private storeIdempotentResult;
    executeSaga<T>(steps: Array<{
        execute: () => Promise<any>;
        compensate: () => Promise<void>;
    }>): Promise<T>;
}
