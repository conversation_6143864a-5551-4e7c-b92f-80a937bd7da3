"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisThrottlerGuard = void 0;
const common_1 = require("@nestjs/common");
const throttler_1 = require("@nestjs/throttler");
const throttler_storage_interface_1 = require("@nestjs/throttler/dist/throttler-storage.interface");
const core_1 = require("@nestjs/core");
const redis_service_1 = require("../services/redis.service");
let RedisThrottlerGuard = class RedisThrottlerGuard extends throttler_1.ThrottlerGuard {
    redisService;
    constructor(options, storageService, reflector, redisService) {
        super(options, storageService, reflector);
        this.redisService = redisService;
    }
    async getTracker(req) {
        return req.ip || req.connection.remoteAddress || 'unknown';
    }
    async getHits(context) {
        const req = context.switchToHttp().getRequest();
        const tracker = await this.getTracker(req);
        const key = `throttle:${tracker}`;
        const hits = await this.redisService.get(key);
        return hits ? parseInt(hits, 10) : 0;
    }
    async storageIncrement(context, limit, ttl) {
        const req = context.switchToHttp().getRequest();
        const tracker = await this.getTracker(req);
        const key = `throttle:${tracker}`;
        const current = await this.redisService.incr(key);
        if (current === 1) {
            await this.redisService.expire(key, ttl);
        }
        return current;
    }
};
exports.RedisThrottlerGuard = RedisThrottlerGuard;
exports.RedisThrottlerGuard = RedisThrottlerGuard = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [Object, Object, core_1.Reflector,
        redis_service_1.RedisService])
], RedisThrottlerGuard);
//# sourceMappingURL=redis-throttler.guard.js.map