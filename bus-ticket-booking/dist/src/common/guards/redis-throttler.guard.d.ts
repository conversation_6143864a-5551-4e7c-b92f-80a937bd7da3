import { ExecutionContext } from '@nestjs/common';
import { ThrottlerGuard, ThrottlerModuleOptions } from '@nestjs/throttler';
import { ThrottlerStorage } from '@nestjs/throttler/dist/throttler-storage.interface';
import { Reflector } from '@nestjs/core';
import { RedisService } from '../services/redis.service';
export declare class RedisThrottlerGuard extends ThrottlerGuard {
    private readonly redisService;
    constructor(options: ThrottlerModuleOptions, storageService: ThrottlerStorage, reflector: Reflector, redisService: RedisService);
    protected getTracker(req: Record<string, any>): Promise<string>;
    protected getHits(context: ExecutionContext): Promise<number>;
    protected storageIncrement(context: ExecutionContext, limit: number, ttl: number): Promise<number>;
}
