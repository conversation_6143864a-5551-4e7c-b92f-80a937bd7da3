version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: bus-booking-postgres-dev
    restart: unless-stopped
    environment:
      POSTGRES_DB: bus_ticket_booking
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql

  redis:
    image: redis:7-alpine
    container_name: bus-booking-redis-dev
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data

volumes:
  postgres_dev_data:
  redis_dev_data:
