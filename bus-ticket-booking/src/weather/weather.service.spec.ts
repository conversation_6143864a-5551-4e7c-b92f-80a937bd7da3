import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { BadRequestException, ServiceUnavailableException } from '@nestjs/common';
import axios from 'axios';
import { WeatherService } from './weather.service';
import { RedisService } from '../common/services/redis.service';

jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('WeatherService', () => {
  let service: WeatherService;
  let redisService: RedisService;
  let configService: ConfigService;

  const mockConfigService = {
    get: jest.fn(),
  };

  const mockRedisService = {
    getJson: jest.fn(),
    setJson: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        WeatherService,
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: RedisService,
          useValue: mockRedisService,
        },
      ],
    }).compile();

    service = module.get<WeatherService>(WeatherService);
    redisService = module.get<RedisService>(RedisService);
    configService = module.get<ConfigService>(ConfigService);

    // Setup default config values
    mockConfigService.get.mockImplementation((key: string) => {
      switch (key) {
        case 'app.openWeather.apiKey':
          return 'test-api-key';
        case 'app.openWeather.baseUrl':
          return 'https://api.openweathermap.org/data/2.5';
        default:
          return undefined;
      }
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getWeatherByCity', () => {
    const mockWeatherResponse = {
      data: {
        name: 'New York',
        sys: { country: 'US' },
        main: {
          temp: 22.5,
          feels_like: 25.0,
          humidity: 65,
          pressure: 1013,
        },
        weather: [
          {
            main: 'Clear',
            description: 'clear sky',
          },
        ],
        wind: {
          speed: 5.2,
          deg: 225,
        },
        visibility: 10000,
      },
    };

    it('should return cached weather data when available', async () => {
      const cachedData = {
        city: 'New York',
        country: 'US',
        temperature: 22.5,
        feelsLike: 'Feels like 25°C',
        description: 'clear sky',
        main: 'clear',
        humidity: 65,
        pressure: 1013,
        windSpeed: 5.2,
        windDirection: 'SW',
        visibility: 10,
        timestamp: '2024-01-15T10:30:00Z',
      };

      mockRedisService.getJson.mockResolvedValue(cachedData);

      const result = await service.getWeatherByCity('New York');

      expect(redisService.getJson).toHaveBeenCalledWith('weather:new york');
      expect(result).toEqual({ ...cachedData, source: 'cached' });
      expect(mockedAxios.get).not.toHaveBeenCalled();
    });

    it('should fetch from API when not in cache', async () => {
      mockRedisService.getJson.mockResolvedValue(null);
      mockedAxios.get.mockResolvedValue(mockWeatherResponse);

      const result = await service.getWeatherByCity('New York');

      expect(redisService.getJson).toHaveBeenCalledWith('weather:new york');
      expect(mockedAxios.get).toHaveBeenCalledWith(
        'https://api.openweathermap.org/data/2.5/weather',
        {
          params: {
            q: 'New York',
            appid: 'test-api-key',
            units: 'metric',
          },
          timeout: 5000,
        },
      );
      expect(redisService.setJson).toHaveBeenCalled();
      expect(result.source).toBe('api');
      expect(result.city).toBe('New York');
      expect(result.temperature).toBe(22.5);
      expect(result.windDirection).toBe('SW');
    });

    it('should throw BadRequestException for empty city name', async () => {
      await expect(service.getWeatherByCity('')).rejects.toThrow(BadRequestException);
      await expect(service.getWeatherByCity('   ')).rejects.toThrow(BadRequestException);
    });

    it('should throw ServiceUnavailableException when API key is not configured', async () => {
      // Create a new service instance with no API key
      const moduleWithoutApiKey: TestingModule = await Test.createTestingModule({
        providers: [
          WeatherService,
          {
            provide: ConfigService,
            useValue: {
              get: jest.fn().mockImplementation((key: string) => {
                if (key === 'app.openWeather.apiKey') return undefined;
                return 'https://api.openweathermap.org/data/2.5';
              }),
            },
          },
          {
            provide: RedisService,
            useValue: mockRedisService,
          },
        ],
      }).compile();

      const serviceWithoutApiKey = moduleWithoutApiKey.get<WeatherService>(WeatherService);
      mockRedisService.getJson.mockResolvedValue(null);

      await expect(serviceWithoutApiKey.getWeatherByCity('New York')).rejects.toThrow(ServiceUnavailableException);
    });

    it('should throw BadRequestException for city not found (404)', async () => {
      mockRedisService.getJson.mockResolvedValue(null);

      const axiosError = new Error('Request failed with status code 404');
      (axiosError as any).isAxiosError = true;
      (axiosError as any).response = { status: 404 };

      mockedAxios.get.mockRejectedValue(axiosError);

      await expect(service.getWeatherByCity('InvalidCity')).rejects.toThrow(BadRequestException);
    });

    it('should throw ServiceUnavailableException for API authentication error (401)', async () => {
      mockRedisService.getJson.mockResolvedValue(null);
      mockedAxios.get.mockRejectedValue({
        isAxiosError: true,
        response: { status: 401 },
      });

      await expect(service.getWeatherByCity('New York')).rejects.toThrow(ServiceUnavailableException);
    });

    it('should handle cache failures gracefully', async () => {
      mockRedisService.getJson.mockRejectedValue(new Error('Redis connection failed'));
      mockRedisService.setJson.mockRejectedValue(new Error('Redis connection failed'));
      mockedAxios.get.mockResolvedValue(mockWeatherResponse);

      const result = await service.getWeatherByCity('New York');

      expect(result.source).toBe('api');
      expect(result.city).toBe('New York');
    });
  });
});
