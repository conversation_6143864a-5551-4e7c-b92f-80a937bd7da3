import { Test, TestingModule } from '@nestjs/testing';
import { ThrottlerGuard } from '@nestjs/throttler';
import { WeatherController } from './weather.controller';
import { WeatherService } from './weather.service';

describe('WeatherController', () => {
  let controller: WeatherController;
  let weatherService: WeatherService;

  const mockWeatherService = {
    getWeatherByCity: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [WeatherController],
      providers: [
        {
          provide: WeatherService,
          useValue: mockWeatherService,
        },
      ],
    })
      .overrideGuard(ThrottlerGuard)
      .useValue({ canActivate: () => true })
      .compile();

    controller = module.get<WeatherController>(WeatherController);
    weatherService = module.get<WeatherService>(WeatherService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getWeather', () => {
    it('should return weather data for a city', async () => {
      const mockWeatherData = {
        city: 'New York',
        country: 'US',
        temperature: 22.5,
        feelsLike: 'Feels like 25°C',
        description: 'clear sky',
        main: 'clear',
        humidity: 65,
        pressure: 1013,
        windSpeed: 5.2,
        windDirection: 'SW',
        visibility: 10,
        timestamp: '2024-01-15T10:30:00Z',
        source: 'api' as const,
      };

      mockWeatherService.getWeatherByCity.mockResolvedValue(mockWeatherData);

      const result = await controller.getWeather('New York');

      expect(weatherService.getWeatherByCity).toHaveBeenCalledWith('New York');
      expect(result).toEqual(mockWeatherData);
    });

    it('should pass city parameter to weather service', async () => {
      const city = 'London';
      const mockWeatherData = {
        city: 'London',
        country: 'GB',
        temperature: 15.0,
        feelsLike: 'Feels like 13°C',
        description: 'light rain',
        main: 'rain',
        humidity: 80,
        pressure: 1008,
        windSpeed: 3.5,
        windDirection: 'W',
        visibility: 8,
        timestamp: '2024-01-15T10:30:00Z',
        source: 'cached' as const,
      };

      mockWeatherService.getWeatherByCity.mockResolvedValue(mockWeatherData);

      const result = await controller.getWeather(city);

      expect(weatherService.getWeatherByCity).toHaveBeenCalledWith(city);
      expect(result).toEqual(mockWeatherData);
    });
  });
});
