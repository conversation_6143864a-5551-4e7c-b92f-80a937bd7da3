import { Controller, Get, Param, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBearerAuth } from '@nestjs/swagger';
import { ThrottlerGuard } from '@nestjs/throttler';
import { WeatherService } from './weather.service';
import { WeatherResponseDto } from '../dto';

@ApiTags('Weather')
@Controller('weather')
@UseGuards(ThrottlerGuard)
export class WeatherController {
  constructor(private readonly weatherService: WeatherService) {}

  @Get(':city')
  @ApiOperation({
    summary: 'Get weather information for a city',
    description: 'Retrieves current weather information for the specified city. Data is cached for 30 minutes to improve performance.'
  })
  @ApiParam({
    name: 'city',
    description: 'City name (letters, spaces, hyphens, and apostrophes only)',
    example: 'New York'
  })
  @ApiResponse({
    status: 200,
    description: 'Weather information retrieved successfully',
    type: WeatherResponseDto
  })
  @ApiResponse({
    status: 400,
    description: 'Invalid city name or city not found'
  })
  @ApiResponse({
    status: 429,
    description: 'Too many requests - rate limit exceeded'
  })
  @ApiResponse({
    status: 503,
    description: 'Weather service unavailable'
  })
  async getWeather(@Param('city') city: string): Promise<WeatherResponseDto> {
    return this.weatherService.getWeatherByCity(city);
  }
}
