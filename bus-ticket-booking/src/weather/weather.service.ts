import { Injectable, Logger, BadRequestException, ServiceUnavailableException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import axios, { AxiosResponse } from 'axios';
import { RedisService } from '../common/services/redis.service';
import { WeatherResponseDto } from '../dto';

interface OpenWeatherMapResponse {
  name: string;
  sys: {
    country: string;
  };
  main: {
    temp: number;
    feels_like: number;
    humidity: number;
    pressure: number;
  };
  weather: Array<{
    main: string;
    description: string;
  }>;
  wind: {
    speed: number;
    deg: number;
  };
  visibility: number;
}

@Injectable()
export class WeatherService {
  private readonly logger = new Logger(WeatherService.name);
  private readonly apiKey: string | undefined;
  private readonly baseUrl: string;
  private readonly cacheTimeout = 30 * 60; // 30 minutes in seconds

  constructor(
    private readonly configService: ConfigService,
    private readonly redisService: RedisService,
  ) {
    this.apiKey = this.configService.get<string>('app.openWeather.apiKey');
    this.baseUrl = this.configService.get<string>('app.openWeather.baseUrl') || 'https://api.openweathermap.org/data/2.5';

    if (!this.apiKey) {
      this.logger.warn('OpenWeatherMap API key not configured');
    }
  }

  async getWeatherByCity(city: string): Promise<WeatherResponseDto> {
    // Validate input
    if (!city || city.trim().length === 0) {
      throw new BadRequestException('City name is required');
    }

    const normalizedCity = city.trim().toLowerCase();
    const cacheKey = `weather:${normalizedCity}`;

    // Try to get from cache first
    try {
      const cachedWeather = await this.redisService.getJson<WeatherResponseDto>(cacheKey);
      if (cachedWeather) {
        this.logger.log(`Weather data for ${city} retrieved from cache`);
        return { ...cachedWeather, source: 'cached' };
      }
    } catch (error) {
      this.logger.warn(`Failed to retrieve weather data from cache for ${city}`, error);
    }

    // If not in cache or cache failed, fetch from API
    if (!this.apiKey) {
      throw new ServiceUnavailableException('Weather service is not configured');
    }

    try {
      const response: AxiosResponse<OpenWeatherMapResponse> = await axios.get(
        `${this.baseUrl}/weather`,
        {
          params: {
            q: city,
            appid: this.apiKey,
            units: 'metric',
          },
          timeout: 5000, // 5 second timeout
        },
      );

      const weatherData = this.transformWeatherData(response.data);

      // Cache the result
      try {
        await this.redisService.setJson(cacheKey, weatherData, this.cacheTimeout);
        this.logger.log(`Weather data for ${city} cached for ${this.cacheTimeout} seconds`);
      } catch (error) {
        this.logger.warn(`Failed to cache weather data for ${city}`, error);
      }

      return { ...weatherData, source: 'api' };
    } catch (error: any) {
      this.logger.error(`Failed to fetch weather data for ${city}`, error);

      if (error.isAxiosError || axios.isAxiosError(error)) {
        if (error.response?.status === 404) {
          throw new BadRequestException(`City "${city}" not found`);
        }
        if (error.response?.status === 401) {
          throw new ServiceUnavailableException('Weather service authentication failed');
        }
        if (error.response?.status === 429) {
          throw new ServiceUnavailableException('Weather service rate limit exceeded');
        }
      }

      throw new ServiceUnavailableException('Weather service is currently unavailable');
    }
  }

  private transformWeatherData(data: OpenWeatherMapResponse): Omit<WeatherResponseDto, 'source'> {
    const windDirection = this.getWindDirection(data.wind.deg);

    return {
      city: data.name,
      country: data.sys.country,
      temperature: Math.round(data.main.temp * 10) / 10,
      feelsLike: `Feels like ${Math.round(data.main.feels_like)}°C`,
      description: data.weather[0].description,
      main: data.weather[0].main.toLowerCase(),
      humidity: data.main.humidity,
      pressure: data.main.pressure,
      windSpeed: data.wind.speed,
      windDirection,
      visibility: Math.round((data.visibility || 0) / 1000), // Convert to km
      timestamp: new Date().toISOString(),
    };
  }

  private getWindDirection(degrees: number): string {
    const directions = ['N', 'NNE', 'NE', 'ENE', 'E', 'ESE', 'SE', 'SSE', 'S', 'SSW', 'SW', 'WSW', 'W', 'WNW', 'NW', 'NNW'];
    const index = Math.round(degrees / 22.5) % 16;
    return directions[index];
  }
}
