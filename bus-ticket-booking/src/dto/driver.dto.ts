import { IsString, IsOptional, <PERSON>Length } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class CreateDriverDto {
  @ApiProperty({ example: '<PERSON>' })
  @IsString()
  @MaxLength(255)
  name: string;

  @ApiProperty({ example: 'DL123456789' })
  @IsString()
  @MaxLength(50)
  licenseNumber: string;

  @ApiProperty({ example: '+**********' })
  @IsString()
  @MaxLength(20)
  phoneNumber: string;
}

export class UpdateDriverDto {
  @ApiPropertyOptional({ example: '<PERSON>' })
  @IsOptional()
  @IsString()
  @MaxLength(255)
  name?: string;

  @ApiPropertyOptional({ example: 'DL123456789' })
  @IsOptional()
  @IsString()
  @MaxLength(50)
  licenseNumber?: string;

  @ApiPropertyOptional({ example: '+**********' })
  @IsOptional()
  @IsString()
  @MaxLength(20)
  phoneNumber?: string;
}

export class DriverResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  licenseNumber: string;

  @ApiProperty()
  phoneNumber: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}
