import { IsString, IsNotEmpty, Matches } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class WeatherQueryDto {
  @ApiProperty({ 
    example: 'New York',
    description: 'City name for weather information'
  })
  @IsString()
  @IsNotEmpty()
  @Matches(/^[a-zA-Z\s\-']+$/, {
    message: 'City name can only contain letters, spaces, hyphens, and apostrophes'
  })
  city: string;
}

export class WeatherResponseDto {
  @ApiProperty({ example: 'New York' })
  city: string;

  @ApiProperty({ example: 'US' })
  country: string;

  @ApiProperty({ example: 22.5 })
  temperature: number;

  @ApiProperty({ example: 'Feels like 25°C' })
  feelsLike: string;

  @ApiProperty({ example: 'Clear sky' })
  description: string;

  @ApiProperty({ example: 'clear' })
  main: string;

  @ApiProperty({ example: 65 })
  humidity: number;

  @ApiProperty({ example: 1013 })
  pressure: number;

  @ApiProperty({ example: 5.2 })
  windSpeed: number;

  @ApiProperty({ example: 'SW' })
  windDirection: string;

  @ApiProperty({ example: 10 })
  visibility: number;

  @ApiProperty({ example: '2024-01-15T10:30:00Z' })
  timestamp: string;

  @ApiProperty({ example: 'cached' })
  source: 'api' | 'cached';
}
