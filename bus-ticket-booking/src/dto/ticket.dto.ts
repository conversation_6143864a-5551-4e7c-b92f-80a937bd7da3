import { IsUUID, IsOptional, IsEnum } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { TicketStatus } from '../entities';

export class CreateTicketDto {
  @ApiProperty()
  @IsUUID()
  tripId: string;

  @ApiProperty()
  @IsUUID()
  seatId: string;
}

export class UpdateTicketDto {
  @ApiPropertyOptional({ enum: TicketStatus })
  @IsOptional()
  @IsEnum(TicketStatus)
  status?: TicketStatus;
}

export class TicketResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  userId: string;

  @ApiProperty()
  tripId: string;

  @ApiProperty()
  seatId: string;

  @ApiProperty()
  bookingDate: Date;

  @ApiProperty({ enum: TicketStatus })
  status: TicketStatus;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;

  @ApiProperty()
  trip?: {
    id: string;
    departure: string;
    destination: string;
    departureTime: Date;
    arrivalTime: Date;
    price: number;
  };

  @ApiProperty()
  seat?: {
    id: string;
    seatNumber: string;
  };
}
