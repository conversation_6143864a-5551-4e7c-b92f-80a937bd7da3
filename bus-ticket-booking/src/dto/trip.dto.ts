import { IsString, IsDate<PERSON>tring, <PERSON><PERSON><PERSON><PERSON>, <PERSON>U<PERSON>D, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON> } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export class CreateTripDto {
  @ApiProperty({ example: 'New York' })
  @IsString()
  departure: string;

  @ApiProperty({ example: 'Boston' })
  @IsString()
  destination: string;

  @ApiProperty({ example: '2024-01-15T08:00:00Z' })
  @IsDateString()
  departureTime: string;

  @ApiProperty({ example: '2024-01-15T12:00:00Z' })
  @IsDateString()
  arrivalTime: string;

  @ApiProperty({ example: 45.99 })
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0.01)
  price: number;

  @ApiProperty({ example: 50 })
  @IsNumber()
  @Min(1)
  @Max(100)
  totalSeats: number;

  @ApiProperty()
  @IsUUID()
  driverId: string;
}

export class UpdateTripDto {
  @ApiPropertyOptional({ example: 'New York' })
  @IsOptional()
  @IsString()
  departure?: string;

  @ApiPropertyOptional({ example: 'Boston' })
  @IsOptional()
  @IsString()
  destination?: string;

  @ApiPropertyOptional({ example: '2024-01-15T08:00:00Z' })
  @IsOptional()
  @IsDateString()
  departureTime?: string;

  @ApiPropertyOptional({ example: '2024-01-15T12:00:00Z' })
  @IsOptional()
  @IsDateString()
  arrivalTime?: string;

  @ApiPropertyOptional({ example: 45.99 })
  @IsOptional()
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0.01)
  price?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsUUID()
  driverId?: string;
}

export class SearchTripsDto {
  @ApiProperty({ example: 'New York' })
  @IsString()
  departure: string;

  @ApiProperty({ example: 'Boston' })
  @IsString()
  destination: string;

  @ApiProperty({ example: '2024-01-15' })
  @IsDateString()
  date: string;

  @ApiPropertyOptional({ example: 1, default: 1 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page?: number = 1;

  @ApiPropertyOptional({ example: 10, default: 10 })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  @Max(50)
  limit?: number = 10;
}

export class TripResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  departure: string;

  @ApiProperty()
  destination: string;

  @ApiProperty()
  departureTime: Date;

  @ApiProperty()
  arrivalTime: Date;

  @ApiProperty()
  price: number;

  @ApiProperty()
  totalSeats: number;

  @ApiProperty()
  availableSeats: number;

  @ApiProperty()
  driverId: string;

  @ApiProperty()
  createdAt: Date;

  @ApiProperty()
  updatedAt: Date;
}
