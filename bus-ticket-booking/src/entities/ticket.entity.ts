import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  OneToOne,
  JoinColumn,
  Index,
} from 'typeorm';
import { User } from './user.entity';
import { Trip } from './trip.entity';
import { Seat } from './seat.entity';

export enum TicketStatus {
  BOOKED = 'BOOKED',
  CANCELLED = 'CANCELLED',
}

@Entity('tickets')
@Index(['userId', 'status'])
@Index(['tripId', 'status'])
@Index(['bookingDate'])
export class Ticket {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  userId: string;

  @Column({ type: 'uuid' })
  tripId: string;

  @Column({ type: 'uuid' })
  seatId: string;

  @Column({ type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  bookingDate: Date;

  @Column({
    type: 'enum',
    enum: TicketStatus,
    default: TicketStatus.BOOKED,
  })
  status: TicketStatus;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @ManyToOne(() => User, (user) => user.tickets, {
    onDelete: 'RESTRICT',
    onUpdate: 'CASCADE',
  })
  @JoinColumn({ name: 'userId' })
  user: User;

  @ManyToOne(() => Trip, (trip) => trip.tickets, {
    onDelete: 'RESTRICT',
    onUpdate: 'CASCADE',
  })
  @JoinColumn({ name: 'tripId' })
  trip: Trip;

  @OneToOne(() => Seat, (seat) => seat.ticket, {
    onDelete: 'RESTRICT',
    onUpdate: 'CASCADE',
  })
  @JoinColumn({ name: 'seatId' })
  seat: Seat;

  // Virtual properties
  get isActive(): boolean {
    return this.status === TicketStatus.BOOKED;
  }

  get isCancelled(): boolean {
    return this.status === TicketStatus.CANCELLED;
  }
}
