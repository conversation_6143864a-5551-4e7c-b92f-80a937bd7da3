import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  VersionColumn,
  ManyToOne,
  OneToOne,
  JoinColumn,
  Index,
  Unique,
} from 'typeorm';
import { Trip } from './trip.entity';
import { Ticket } from './ticket.entity';

@Entity('seats')
@Unique(['tripId', 'seatNumber'])
@Index(['tripId', 'isBooked'])
export class Seat {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 10 })
  seatNumber: string;

  @Column({ type: 'boolean', default: false })
  isBooked: boolean;

  @Column({ type: 'uuid' })
  tripId: string;

  @VersionColumn()
  version: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @ManyToOne(() => Trip, (trip) => trip.seats, {
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE',
  })
  @JoinColumn({ name: 'tripId' })
  trip: Trip;

  @OneToOne(() => Ticket, (ticket) => ticket.seat)
  ticket: Ticket;
}
