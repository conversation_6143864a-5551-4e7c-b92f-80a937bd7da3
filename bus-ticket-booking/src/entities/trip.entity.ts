import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  VersionColumn,
  ManyToOne,
  OneToMany,
  JoinColumn,
  Index,
  Check,
} from 'typeorm';
import { Driver } from './driver.entity';
import { Seat } from './seat.entity';
import { Ticket } from './ticket.entity';

@Entity('trips')
@Index(['departure', 'destination', 'departureTime'])
@Check(`"departureTime" < "arrivalTime"`)
@Check(`"totalSeats" > 0`)
@Check(`"availableSeats" >= 0`)
@Check(`"availableSeats" <= "totalSeats"`)
@Check(`"price" > 0`)
export class Trip {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'varchar', length: 255 })
  departure: string;

  @Column({ type: 'varchar', length: 255 })
  destination: string;

  @Column({ type: 'timestamp' })
  departureTime: Date;

  @Column({ type: 'timestamp' })
  arrivalTime: Date;

  @Column({ type: 'decimal', precision: 10, scale: 2 })
  price: number;

  @Column({ type: 'int' })
  totalSeats: number;

  @Column({ type: 'int' })
  availableSeats: number;

  @Column({ type: 'uuid' })
  driverId: string;

  @VersionColumn()
  version: number;

  @CreateDateColumn()
  createdAt: Date;

  @UpdateDateColumn()
  updatedAt: Date;

  // Relations
  @ManyToOne(() => Driver, (driver) => driver.trips, {
    onDelete: 'RESTRICT',
    onUpdate: 'CASCADE',
  })
  @JoinColumn({ name: 'driverId' })
  driver: Driver;

  @OneToMany(() => Seat, (seat) => seat.trip, {
    cascade: true,
  })
  seats: Seat[];

  @OneToMany(() => Ticket, (ticket) => ticket.trip)
  tickets: Ticket[];

  // Virtual properties
  get duration(): number {
    return this.arrivalTime.getTime() - this.departureTime.getTime();
  }

  get isFullyBooked(): boolean {
    return this.availableSeats === 0;
  }

  get occupancyRate(): number {
    return ((this.totalSeats - this.availableSeats) / this.totalSeats) * 100;
  }
}
