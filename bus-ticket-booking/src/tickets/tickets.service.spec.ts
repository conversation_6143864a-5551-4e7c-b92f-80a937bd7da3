import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { TicketsService } from './tickets.service';
import { Ticket, Seat, Trip, User, TicketStatus } from '../entities';

describe('TicketsService', () => {
  let service: TicketsService;

  const mockTicketRepository = {
    find: jest.fn(),
    findOne: jest.fn(),
    findAndCount: jest.fn(),
  };

  const mockSeatRepository = {};
  const mockTripRepository = {};
  const mockUserRepository = {};

  const mockDataSource = {
    transaction: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TicketsService,
        {
          provide: getRepositoryToken(Ticket),
          useValue: mockTicketRepository,
        },
        {
          provide: getRepositoryToken(Seat),
          useValue: mockSeatRepository,
        },
        {
          provide: getRepositoryToken(Trip),
          useValue: mockTripRepository,
        },
        {
          provide: getRepositoryToken(User),
          useValue: mockUserRepository,
        },
        {
          provide: DataSource,
          useValue: mockDataSource,
        },
      ],
    }).compile();

    service = module.get<TicketsService>(TicketsService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('bookTicket', () => {
    const userId = 'user-id';
    const createTicketDto = {
      tripId: 'trip-id',
      seatId: 'seat-id',
    };

    it('should book a ticket successfully', async () => {
      const seat = { id: 'seat-id', isBooked: false, tripId: 'trip-id' };
      const trip = { id: 'trip-id', availableSeats: 5, departureTime: new Date(Date.now() + 86400000) };
      const user = { id: 'user-id', email: '<EMAIL>' };
      const ticket = { id: 'ticket-id', userId, tripId: 'trip-id', seatId: 'seat-id' };

      const mockManager = {
        findOne: jest.fn()
          .mockResolvedValueOnce(seat) // seat lookup
          .mockResolvedValueOnce(trip) // trip lookup
          .mockResolvedValueOnce(user) // user lookup
          .mockResolvedValueOnce(null), // existing ticket lookup
        create: jest.fn().mockReturnValue(ticket),
        save: jest.fn().mockResolvedValue(ticket),
      };

      mockDataSource.transaction.mockImplementation(async (callback) => {
        return callback(mockManager);
      });

      const result = await service.bookTicket(userId, createTicketDto);

      expect(mockDataSource.transaction).toHaveBeenCalled();
      expect(result).toEqual(ticket);
    });

    it('should throw ConflictException if seat is already booked', async () => {
      const seat = { id: 'seat-id', isBooked: true, tripId: 'trip-id' };

      const mockManager = {
        findOne: jest.fn().mockResolvedValue(seat),
      };

      mockDataSource.transaction.mockImplementation(async (callback) => {
        return callback(mockManager);
      });

      await expect(service.bookTicket(userId, createTicketDto)).rejects.toThrow(ConflictException);
    });

    it('should throw NotFoundException if seat not found', async () => {
      const mockManager = {
        findOne: jest.fn().mockResolvedValue(null),
      };

      mockDataSource.transaction.mockImplementation(async (callback) => {
        return callback(mockManager);
      });

      await expect(service.bookTicket(userId, createTicketDto)).rejects.toThrow(NotFoundException);
    });
  });

  describe('cancelTicket', () => {
    const userId = 'user-id';
    const ticketId = 'ticket-id';

    it('should cancel a ticket successfully', async () => {
      const futureDate = new Date(Date.now() + 4 * 60 * 60 * 1000); // 4 hours from now
      const ticket = {
        id: ticketId,
        userId,
        status: TicketStatus.BOOKED,
        trip: { departureTime: futureDate },
        seatId: 'seat-id',
        tripId: 'trip-id',
      };

      const mockManager = {
        findOne: jest.fn()
          .mockResolvedValueOnce(ticket) // ticket lookup
          .mockResolvedValueOnce({ id: 'seat-id', isBooked: true }) // seat lookup
          .mockResolvedValueOnce({ id: 'trip-id', availableSeats: 5 }), // trip lookup
        save: jest.fn().mockResolvedValue({ ...ticket, status: TicketStatus.CANCELLED }),
      };

      mockDataSource.transaction.mockImplementation(async (callback) => {
        return callback(mockManager);
      });

      const result = await service.cancelTicket(userId, ticketId);

      expect(mockDataSource.transaction).toHaveBeenCalled();
      expect(result.status).toBe(TicketStatus.CANCELLED);
    });

    it('should throw BadRequestException if cancellation is too close to departure', async () => {
      const nearFutureDate = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes from now
      const ticket = {
        id: ticketId,
        userId,
        status: TicketStatus.BOOKED,
        trip: { departureTime: nearFutureDate },
      };

      const mockManager = {
        findOne: jest.fn().mockResolvedValue(ticket),
      };

      mockDataSource.transaction.mockImplementation(async (callback) => {
        return callback(mockManager);
      });

      await expect(service.cancelTicket(userId, ticketId)).rejects.toThrow(BadRequestException);
    });
  });

  describe('getUserBookings', () => {
    it('should return user bookings', async () => {
      const userId = 'user-id';
      const tickets = [
        { id: 'ticket-1', userId },
        { id: 'ticket-2', userId },
      ];

      mockTicketRepository.find.mockResolvedValue(tickets);

      const result = await service.getUserBookings(userId);

      expect(mockTicketRepository.find).toHaveBeenCalledWith({
        where: { userId },
        relations: ['trip', 'seat', 'trip.driver'],
        order: { bookingDate: 'DESC' },
      });
      expect(result).toEqual(tickets);
    });
  });

  describe('findOne', () => {
    it('should return a ticket if found', async () => {
      const ticket = { id: 'ticket-id', userId: 'user-id' };
      mockTicketRepository.findOne.mockResolvedValue(ticket);

      const result = await service.findOne('ticket-id');

      expect(mockTicketRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'ticket-id' },
        relations: ['trip', 'seat', 'user', 'trip.driver'],
      });
      expect(result).toEqual(ticket);
    });

    it('should throw NotFoundException if ticket not found', async () => {
      mockTicketRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne('ticket-id')).rejects.toThrow(NotFoundException);
    });
  });
});
