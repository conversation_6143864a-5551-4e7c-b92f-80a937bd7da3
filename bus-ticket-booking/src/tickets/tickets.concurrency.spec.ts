import { Test, TestingModule } from '@nestjs/testing';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DataSource } from 'typeorm';
import { ConflictException } from '@nestjs/common';
import { TicketsService } from './tickets.service';
import { Ticket, Seat, Trip, User, TicketStatus, UserRole } from '../entities';

describe('TicketsService - Concurrency Tests', () => {
  let service: TicketsService;
  let dataSource: DataSource;
  let module: TestingModule;

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        TypeOrmModule.forRoot({
          type: 'sqlite',
          database: ':memory:',
          entities: [Ticket, Seat, Trip, User],
          synchronize: true,
          logging: false,
        }),
        TypeOrmModule.forFeature([Ticket, Seat, Trip, User]),
      ],
      providers: [TicketsService],
    }).compile();

    service = module.get<TicketsService>(TicketsService);
    dataSource = module.get<DataSource>(DataSource);
  });

  afterAll(async () => {
    await module.close();
  });

  beforeEach(async () => {
    // Clean up database before each test
    await dataSource.query('DELETE FROM tickets');
    await dataSource.query('DELETE FROM seats');
    await dataSource.query('DELETE FROM trips');
    await dataSource.query('DELETE FROM users');
  });

  describe('Concurrent Booking Prevention', () => {
    it('should prevent double booking with pessimistic locking', async () => {
      // Setup test data
      const user1 = await dataSource.getRepository(User).save({
        email: '<EMAIL>',
        password: 'hashedpassword',
        firstName: 'User',
        lastName: 'One',
        role: UserRole.USER,
      });

      const user2 = await dataSource.getRepository(User).save({
        email: '<EMAIL>',
        password: 'hashedpassword',
        firstName: 'User',
        lastName: 'Two',
        role: UserRole.USER,
      });

      const trip = await dataSource.getRepository(Trip).save({
        departure: 'New York',
        destination: 'Boston',
        departureTime: new Date(Date.now() + 86400000), // Tomorrow
        arrivalTime: new Date(Date.now() + 90000000), // Tomorrow + 4 hours
        price: 50.99,
        totalSeats: 2,
        availableSeats: 2,
        driverId: 'driver-id',
      });

      const seat = await dataSource.getRepository(Seat).save({
        seatNumber: 'A01',
        tripId: trip.id,
        isBooked: false,
      });

      // Simulate concurrent booking attempts
      const bookingPromises = [
        service.bookTicket(user1.id, { tripId: trip.id, seatId: seat.id }),
        service.bookTicket(user2.id, { tripId: trip.id, seatId: seat.id }),
      ];

      // One should succeed, one should fail
      const results = await Promise.allSettled(bookingPromises);

      const successCount = results.filter(r => r.status === 'fulfilled').length;
      const failureCount = results.filter(r => r.status === 'rejected').length;

      expect(successCount).toBe(1);
      expect(failureCount).toBe(1);

      // Check that the failed booking threw ConflictException
      const rejectedResult = results.find(r => r.status === 'rejected') as PromiseRejectedResult;
      expect(rejectedResult.reason).toBeInstanceOf(ConflictException);

      // Verify seat is marked as booked
      const updatedSeat = await dataSource.getRepository(Seat).findOne({ where: { id: seat.id } });
      expect(updatedSeat?.isBooked).toBe(true);

      // Verify trip available seats is decremented
      const updatedTrip = await dataSource.getRepository(Trip).findOne({ where: { id: trip.id } });
      expect(updatedTrip?.availableSeats).toBe(1);
    });

    it('should handle multiple concurrent bookings for different seats', async () => {
      // Setup test data with multiple seats
      const user1 = await dataSource.getRepository(User).save({
        email: '<EMAIL>',
        password: 'hashedpassword',
        firstName: 'User',
        lastName: 'One',
        role: UserRole.USER,
      });

      const user2 = await dataSource.getRepository(User).save({
        email: '<EMAIL>',
        password: 'hashedpassword',
        firstName: 'User',
        lastName: 'Two',
        role: UserRole.USER,
      });

      const user3 = await dataSource.getRepository(User).save({
        email: '<EMAIL>',
        password: 'hashedpassword',
        firstName: 'User',
        lastName: 'Three',
        role: UserRole.USER,
      });

      const users = [user1, user2, user3];

      const trip = await dataSource.getRepository(Trip).save({
        departure: 'New York',
        destination: 'Boston',
        departureTime: new Date(Date.now() + 86400000), // Tomorrow
        arrivalTime: new Date(Date.now() + 90000000), // Tomorrow + 4 hours
        price: 50.99,
        totalSeats: 3,
        availableSeats: 3,
        driverId: 'driver-id',
      });

      const seats = await Promise.all([
        dataSource.getRepository(Seat).save({
          seatNumber: 'A01',
          tripId: trip.id,
          isBooked: false,
        }),
        dataSource.getRepository(Seat).save({
          seatNumber: 'A02',
          tripId: trip.id,
          isBooked: false,
        }),
        dataSource.getRepository(Seat).save({
          seatNumber: 'A03',
          tripId: trip.id,
          isBooked: false,
        }),
      ]);

      // Simulate concurrent booking attempts for different seats
      const bookingPromises = users.map((user, index) =>
        service.bookTicket(user.id, { tripId: trip.id, seatId: seats[index].id })
      );

      // All should succeed since they're booking different seats
      const results = await Promise.allSettled(bookingPromises);

      const successCount = results.filter(r => r.status === 'fulfilled').length;
      expect(successCount).toBe(3);

      // Verify all seats are booked
      const updatedSeats = await dataSource.getRepository(Seat).find({ where: { tripId: trip.id } });
      expect(updatedSeats.every(seat => seat.isBooked)).toBe(true);

      // Verify trip available seats is decremented correctly
      const updatedTrip = await dataSource.getRepository(Trip).findOne({ where: { id: trip.id } });
      expect(updatedTrip?.availableSeats).toBe(0);
    });

    it('should handle version conflicts with optimistic locking', async () => {
      // This test demonstrates how version columns help detect concurrent modifications
      const user = await dataSource.getRepository(User).save({
        email: '<EMAIL>',
        password: 'hashedpassword',
        firstName: 'User',
        lastName: 'Test',
        role: UserRole.USER,
      });

      const trip = await dataSource.getRepository(Trip).save({
        departure: 'New York',
        destination: 'Boston',
        departureTime: new Date(Date.now() + 86400000),
        arrivalTime: new Date(Date.now() + 90000000),
        price: 50.99,
        totalSeats: 1,
        availableSeats: 1,
        driverId: 'driver-id',
      });

      const seat = await dataSource.getRepository(Seat).save({
        seatNumber: 'A01',
        tripId: trip.id,
        isBooked: false,
      });

      // Get initial version
      const initialSeat = await dataSource.getRepository(Seat).findOne({ where: { id: seat.id } });
      expect(initialSeat?.version).toBeDefined();

      // Book the seat
      await service.bookTicket(user.id, { tripId: trip.id, seatId: seat.id });

      // Check version has been incremented
      const updatedSeat = await dataSource.getRepository(Seat).findOne({ where: { id: seat.id } });
      expect(updatedSeat?.version).toBeGreaterThan(initialSeat?.version || 0);
    });
  });
});
