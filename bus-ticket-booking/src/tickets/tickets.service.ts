import { Injectable, NotFoundException, BadRequestException, ConflictException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { Ticket, Seat, Trip, User, TicketStatus } from '../entities';
import { CreateTicketDto } from '../dto';

@Injectable()
export class TicketsService {
  constructor(
    @InjectRepository(Ticket)
    private readonly ticketRepository: Repository<Ticket>,
    @InjectRepository(Seat)
    private readonly seatRepository: Repository<Seat>,
    @InjectRepository(Trip)
    private readonly tripRepository: Repository<Trip>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly dataSource: DataSource,
  ) {}

  async bookTicket(userId: string, createTicketDto: CreateTicketDto): Promise<Ticket> {
    const { tripId, seatId } = createTicketDto;

    return await this.dataSource.transaction(async (manager) => {
      // Lock the seat for update to prevent race conditions
      const seat = await manager.findOne(Seat, {
        where: { id: seatId, tripId },
        lock: { mode: 'pessimistic_write' },
      });

      if (!seat) {
        throw new NotFoundException('Seat not found for this trip');
      }

      if (seat.isBooked) {
        throw new ConflictException('Seat is already booked');
      }

      // Verify trip exists and is bookable
      const trip = await manager.findOne(Trip, {
        where: { id: tripId },
        lock: { mode: 'pessimistic_write' },
      });

      if (!trip) {
        throw new NotFoundException('Trip not found');
      }

      if (trip.availableSeats <= 0) {
        throw new ConflictException('No available seats on this trip');
      }

      // Check if departure time is in the future
      if (trip.departureTime <= new Date()) {
        throw new BadRequestException('Cannot book tickets for past trips');
      }

      // Verify user exists
      const user = await manager.findOne(User, { where: { id: userId } });
      if (!user) {
        throw new NotFoundException('User not found');
      }

      // Check if user already has a booking for this trip
      const existingTicket = await manager.findOne(Ticket, {
        where: { userId, tripId, status: TicketStatus.BOOKED },
      });

      if (existingTicket) {
        throw new ConflictException('User already has a booking for this trip');
      }

      // Create the ticket
      const ticket = manager.create(Ticket, {
        userId,
        tripId,
        seatId,
        status: TicketStatus.BOOKED,
        bookingDate: new Date(),
      });

      const savedTicket = await manager.save(Ticket, ticket);

      // Update seat status
      seat.isBooked = true;
      await manager.save(Seat, seat);

      // Update trip available seats
      trip.availableSeats -= 1;
      await manager.save(Trip, trip);

      return savedTicket;
    });
  }

  async cancelTicket(userId: string, ticketId: string): Promise<Ticket> {
    return await this.dataSource.transaction(async (manager) => {
      // Find the ticket with lock
      const ticket = await manager.findOne(Ticket, {
        where: { id: ticketId, userId },
        relations: ['trip', 'seat'],
        lock: { mode: 'pessimistic_write' },
      });

      if (!ticket) {
        throw new NotFoundException('Ticket not found');
      }

      if (ticket.status === TicketStatus.CANCELLED) {
        throw new BadRequestException('Ticket is already cancelled');
      }

      // Check if trip departure is more than 2 hours away
      const now = new Date();
      const departureTime = new Date(ticket.trip.departureTime);
      const timeDifference = departureTime.getTime() - now.getTime();
      const hoursUntilDeparture = timeDifference / (1000 * 60 * 60);

      if (hoursUntilDeparture < 2) {
        throw new BadRequestException('Cannot cancel ticket less than 2 hours before departure');
      }

      // Update ticket status
      ticket.status = TicketStatus.CANCELLED;
      const updatedTicket = await manager.save(Ticket, ticket);

      // Update seat status
      const seat = await manager.findOne(Seat, {
        where: { id: ticket.seatId },
        lock: { mode: 'pessimistic_write' },
      });

      if (seat) {
        seat.isBooked = false;
        await manager.save(Seat, seat);
      }

      // Update trip available seats
      const trip = await manager.findOne(Trip, {
        where: { id: ticket.tripId },
        lock: { mode: 'pessimistic_write' },
      });

      if (trip) {
        trip.availableSeats += 1;
        await manager.save(Trip, trip);
      }

      return updatedTicket;
    });
  }

  async getUserBookings(userId: string): Promise<Ticket[]> {
    return this.ticketRepository.find({
      where: { userId },
      relations: ['trip', 'seat', 'trip.driver'],
      order: { bookingDate: 'DESC' },
    });
  }

  async findOne(id: string): Promise<Ticket> {
    const ticket = await this.ticketRepository.findOne({
      where: { id },
      relations: ['trip', 'seat', 'user', 'trip.driver'],
    });

    if (!ticket) {
      throw new NotFoundException('Ticket not found');
    }

    return ticket;
  }

  async findAll(page: number = 1, limit: number = 10): Promise<{ tickets: Ticket[]; total: number; page: number; limit: number }> {
    const [tickets, total] = await this.ticketRepository.findAndCount({
      relations: ['trip', 'seat', 'user'],
      skip: (page - 1) * limit,
      take: limit,
      order: { bookingDate: 'DESC' },
    });

    return { tickets, total, page, limit };
  }
}
