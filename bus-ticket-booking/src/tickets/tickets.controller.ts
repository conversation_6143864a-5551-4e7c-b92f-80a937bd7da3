import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Query,
  ParseUUIDPipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiQuery } from '@nestjs/swagger';
import { TicketsService } from './tickets.service';
import { CreateTicketDto, TicketResponseDto } from '../dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { RolesGuard } from '../auth/guards/roles.guard';
import { CurrentUser } from '../auth/decorators/current-user.decorator';
import { Roles } from '../auth/decorators/roles.decorator';
import { User, UserRole } from '../entities';

@ApiTags('Tickets')
@Controller('tickets')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class TicketsController {
  constructor(private readonly ticketsService: TicketsService) {}

  @Post('book')
  @ApiOperation({ summary: 'Book a ticket' })
  @ApiResponse({ status: 201, description: 'Ticket booked successfully', type: TicketResponseDto })
  @ApiResponse({ status: 404, description: 'Trip or seat not found' })
  @ApiResponse({ status: 409, description: 'Seat already booked or user already has booking' })
  async bookTicket(@CurrentUser() user: User, @Body() createTicketDto: CreateTicketDto) {
    return this.ticketsService.bookTicket(user.id, createTicketDto);
  }

  @Delete(':id/cancel')
  @ApiOperation({ summary: 'Cancel a ticket' })
  @ApiResponse({ status: 200, description: 'Ticket cancelled successfully', type: TicketResponseDto })
  @ApiResponse({ status: 404, description: 'Ticket not found' })
  @ApiResponse({ status: 400, description: 'Cannot cancel ticket (too close to departure)' })
  async cancelTicket(@CurrentUser() user: User, @Param('id', ParseUUIDPipe) id: string) {
    return this.ticketsService.cancelTicket(user.id, id);
  }

  @Get('my-bookings')
  @ApiOperation({ summary: 'Get current user bookings' })
  @ApiResponse({ status: 200, description: 'User bookings retrieved successfully', type: [TicketResponseDto] })
  async getMyBookings(@CurrentUser() user: User) {
    return this.ticketsService.getUserBookings(user.id);
  }

  @Get()
  @UseGuards(RolesGuard)
  @Roles(UserRole.ADMIN)
  @ApiOperation({ summary: 'Get all tickets (Admin only)' })
  @ApiResponse({ status: 200, description: 'Tickets retrieved successfully', type: [TicketResponseDto] })
  @ApiResponse({ status: 403, description: 'Forbidden - Admin access required' })
  @ApiQuery({ name: 'page', required: false, description: 'Page number', example: 1 })
  @ApiQuery({ name: 'limit', required: false, description: 'Items per page', example: 10 })
  async findAll(@Query('page') page?: number, @Query('limit') limit?: number) {
    return this.ticketsService.findAll(page, limit);
  }

  @Get(':id')
  @ApiOperation({ summary: 'Get ticket by ID' })
  @ApiResponse({ status: 200, description: 'Ticket found successfully', type: TicketResponseDto })
  @ApiResponse({ status: 404, description: 'Ticket not found' })
  async findOne(@Param('id', ParseUUIDPipe) id: string) {
    return this.ticketsService.findOne(id);
  }
}
