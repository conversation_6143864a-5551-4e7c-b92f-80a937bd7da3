import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Driver, Trip } from '../../entities';
import { CreateDriverDto, UpdateDriverDto } from '../../dto';

@Injectable()
export class DriversService {
  constructor(
    @InjectRepository(Driver)
    private readonly driverRepository: Repository<Driver>,
    @InjectRepository(Trip)
    private readonly tripRepository: Repository<Trip>,
  ) {}

  async create(createDriverDto: CreateDriverDto): Promise<Driver> {
    // Check if license number already exists
    const existingDriver = await this.driverRepository.findOne({
      where: { licenseNumber: createDriverDto.licenseNumber },
    });

    if (existingDriver) {
      throw new ConflictException('Driver with this license number already exists');
    }

    const driver = this.driverRepository.create(createDriverDto);
    return this.driverRepository.save(driver);
  }

  async findAll(page: number = 1, limit: number = 10): Promise<{ drivers: Driver[]; total: number; page: number; limit: number }> {
    const [drivers, total] = await this.driverRepository.findAndCount({
      relations: ['trips'],
      skip: (page - 1) * limit,
      take: limit,
      order: { createdAt: 'DESC' },
    });

    return { drivers, total, page, limit };
  }

  async findOne(id: string): Promise<Driver> {
    const driver = await this.driverRepository.findOne({
      where: { id },
      relations: ['trips'],
    });

    if (!driver) {
      throw new NotFoundException('Driver not found');
    }

    return driver;
  }

  async update(id: string, updateDriverDto: UpdateDriverDto): Promise<Driver> {
    const driver = await this.findOne(id);

    // Check if license number is being updated and if it conflicts
    if (updateDriverDto.licenseNumber && updateDriverDto.licenseNumber !== driver.licenseNumber) {
      const existingDriver = await this.driverRepository.findOne({
        where: { licenseNumber: updateDriverDto.licenseNumber },
      });

      if (existingDriver) {
        throw new ConflictException('Driver with this license number already exists');
      }
    }

    Object.assign(driver, updateDriverDto);
    return this.driverRepository.save(driver);
  }

  async remove(id: string): Promise<void> {
    const driver = await this.findOne(id);

    // Check if driver has any active trips
    const activeTrips = await this.tripRepository.count({
      where: { driverId: id, departureTime: new Date() },
    });

    if (activeTrips > 0) {
      throw new BadRequestException('Cannot delete driver with active trips');
    }

    await this.driverRepository.remove(driver);
  }
}
