import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { DriversService } from './drivers/drivers.service';
import { DriversController } from './drivers/drivers.controller';
import { Driver, Trip } from '../entities';

@Module({
  imports: [TypeOrmModule.forFeature([Driver, Trip])],
  providers: [DriversService],
  controllers: [DriversController],
  exports: [DriversService],
})
export class AdminModule {}
