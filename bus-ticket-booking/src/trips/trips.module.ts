import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TripsService } from './trips.service';
import { TripsController } from './trips.controller';
import { <PERSON>, <PERSON>t, Driver } from '../entities';

@Module({
  imports: [TypeOrmModule.forFeature([Trip, Seat, Driver])],
  providers: [TripsService],
  controllers: [TripsController],
  exports: [TripsService],
})
export class TripsModule {}
