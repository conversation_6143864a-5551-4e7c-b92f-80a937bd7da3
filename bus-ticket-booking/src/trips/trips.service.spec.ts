import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { NotFoundException, BadRequestException } from '@nestjs/common';
import { TripsService } from './trips.service';
import { <PERSON>, Seat, Driver } from '../entities';

describe('TripsService', () => {
  let service: TripsService;

  const mockTripRepository = {
    create: jest.fn(),
    save: jest.fn(),
    findAndCount: jest.fn(),
    findOne: jest.fn(),
    remove: jest.fn(),
  };

  const mockSeatRepository = {
    create: jest.fn(),
    save: jest.fn(),
    count: jest.fn(),
  };

  const mockDriverRepository = {
    findOne: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TripsService,
        {
          provide: getRepositoryToken(Trip),
          useValue: mockTripRepository,
        },
        {
          provide: getRepositoryToken(Seat),
          useValue: mockSeatRepository,
        },
        {
          provide: getRepositoryToken(Driver),
          useValue: mockDriverRepository,
        },
      ],
    }).compile();

    service = module.get<TripsService>(TripsService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('create', () => {
    const futureDate = new Date();
    futureDate.setDate(futureDate.getDate() + 1); // Tomorrow
    const futureDateString = futureDate.toISOString();
    const laterFutureDate = new Date(futureDate);
    laterFutureDate.setHours(laterFutureDate.getHours() + 4);
    const laterFutureDateString = laterFutureDate.toISOString();

    const createTripDto = {
      departure: 'New York',
      destination: 'Boston',
      departureTime: futureDateString,
      arrivalTime: laterFutureDateString,
      price: 50.99,
      totalSeats: 40,
      driverId: 'driver-id',
    };

    it('should create a trip successfully', async () => {
      const driver = { id: 'driver-id', name: 'John Doe' };
      const savedTrip = { id: 'trip-id', ...createTripDto, availableSeats: 40 };

      mockDriverRepository.findOne.mockResolvedValue(driver);
      mockTripRepository.create.mockReturnValue(savedTrip);
      mockTripRepository.save.mockResolvedValue(savedTrip);
      mockSeatRepository.create.mockImplementation((data) => data);
      mockSeatRepository.save.mockResolvedValue([]);
      mockTripRepository.findOne.mockResolvedValue(savedTrip);

      const result = await service.create(createTripDto);

      expect(mockDriverRepository.findOne).toHaveBeenCalledWith({ where: { id: createTripDto.driverId } });
      expect(mockTripRepository.create).toHaveBeenCalled();
      expect(mockTripRepository.save).toHaveBeenCalled();
      expect(mockSeatRepository.save).toHaveBeenCalled();
      expect(result).toEqual(savedTrip);
    });

    it('should throw NotFoundException if driver not found', async () => {
      mockDriverRepository.findOne.mockResolvedValue(null);

      await expect(service.create(createTripDto)).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException if departure time is after arrival time', async () => {
      const invalidDto = {
        ...createTripDto,
        departureTime: '2024-12-31T14:00:00Z',
        arrivalTime: '2024-12-31T10:00:00Z',
      };

      const driver = { id: 'driver-id', name: 'John Doe' };
      mockDriverRepository.findOne.mockResolvedValue(driver);

      await expect(service.create(invalidDto)).rejects.toThrow(BadRequestException);
    });
  });

  describe('findOne', () => {
    it('should return a trip if found', async () => {
      const trip = { id: 'trip-id', departure: 'New York' };
      mockTripRepository.findOne.mockResolvedValue(trip);

      const result = await service.findOne('trip-id');

      expect(mockTripRepository.findOne).toHaveBeenCalledWith({
        where: { id: 'trip-id' },
        relations: ['driver', 'seats'],
      });
      expect(result).toEqual(trip);
    });

    it('should throw NotFoundException if trip not found', async () => {
      mockTripRepository.findOne.mockResolvedValue(null);

      await expect(service.findOne('trip-id')).rejects.toThrow(NotFoundException);
    });
  });

  describe('search', () => {
    const searchDto = {
      departure: 'New York',
      destination: 'Boston',
      date: '2024-12-31',
      page: 1,
      limit: 10,
    };

    it('should return search results', async () => {
      const trips = [{ id: 'trip-1' }, { id: 'trip-2' }];
      mockTripRepository.findAndCount.mockResolvedValue([trips, 2]);

      const result = await service.search(searchDto);

      expect(mockTripRepository.findAndCount).toHaveBeenCalled();
      expect(result).toEqual({
        trips,
        total: 2,
        page: 1,
        limit: 10,
      });
    });
  });

  describe('remove', () => {
    it('should remove trip if no bookings exist', async () => {
      const trip = { id: 'trip-id', departure: 'New York' };
      mockTripRepository.findOne.mockResolvedValue(trip);
      mockSeatRepository.count.mockResolvedValue(0);
      mockTripRepository.remove.mockResolvedValue(undefined);

      await service.remove('trip-id');

      expect(mockSeatRepository.count).toHaveBeenCalledWith({
        where: { tripId: 'trip-id', isBooked: true },
      });
      expect(mockTripRepository.remove).toHaveBeenCalledWith(trip);
    });

    it('should throw BadRequestException if trip has bookings', async () => {
      const trip = { id: 'trip-id', departure: 'New York' };
      mockTripRepository.findOne.mockResolvedValue(trip);
      mockSeatRepository.count.mockResolvedValue(5);

      await expect(service.remove('trip-id')).rejects.toThrow(BadRequestException);
    });
  });
});
