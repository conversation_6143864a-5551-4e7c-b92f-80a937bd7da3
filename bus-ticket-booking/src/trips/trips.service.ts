import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between, MoreThanOrEqual } from 'typeorm';
import { Trip, Seat, Driver } from '../entities';
import { CreateTripDto, UpdateTripDto, SearchTripsDto } from '../dto';

@Injectable()
export class TripsService {
  constructor(
    @InjectRepository(Trip)
    private readonly tripRepository: Repository<Trip>,
    @InjectRepository(Seat)
    private readonly seatRepository: Repository<Seat>,
    @InjectRepository(Driver)
    private readonly driverRepository: Repository<Driver>,
  ) {}

  async create(createTripDto: CreateTripDto): Promise<Trip> {
    const { driverId, totalSeats, departureTime, arrivalTime, ...tripData } = createTripDto;

    // Validate driver exists
    const driver = await this.driverRepository.findOne({ where: { id: driverId } });
    if (!driver) {
      throw new NotFoundException('Driver not found');
    }

    // Validate departure time is before arrival time
    const departure = new Date(departureTime);
    const arrival = new Date(arrivalTime);
    if (departure >= arrival) {
      throw new BadRequestException('Departure time must be before arrival time');
    }

    // Validate departure time is in the future
    if (departure <= new Date()) {
      throw new BadRequestException('Departure time must be in the future');
    }

    // Create trip
    const trip = this.tripRepository.create({
      ...tripData,
      departureTime: departure,
      arrivalTime: arrival,
      driverId,
      totalSeats,
      availableSeats: totalSeats,
    });

    const savedTrip = await this.tripRepository.save(trip);

    // Create seats for the trip
    const seats = [];
    for (let i = 1; i <= totalSeats; i++) {
      const seat = this.seatRepository.create({
        seatNumber: `A${i.toString().padStart(2, '0')}`,
        tripId: savedTrip.id,
        isBooked: false,
      });
      seats.push(seat);
    }

    await this.seatRepository.save(seats);

    return this.findOne(savedTrip.id);
  }

  async findAll(page: number = 1, limit: number = 10): Promise<{ trips: Trip[]; total: number; page: number; limit: number }> {
    const [trips, total] = await this.tripRepository.findAndCount({
      relations: ['driver', 'seats'],
      skip: (page - 1) * limit,
      take: limit,
      order: { departureTime: 'ASC' },
    });

    return { trips, total, page, limit };
  }

  async findOne(id: string): Promise<Trip> {
    const trip = await this.tripRepository.findOne({
      where: { id },
      relations: ['driver', 'seats'],
    });

    if (!trip) {
      throw new NotFoundException('Trip not found');
    }

    return trip;
  }

  async search(searchDto: SearchTripsDto): Promise<{ trips: Trip[]; total: number; page: number; limit: number }> {
    const { departure, destination, date, page = 1, limit = 10 } = searchDto;

    // Parse the date and create date range for the entire day
    const searchDate = new Date(date);
    const startOfDay = new Date(searchDate);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(searchDate);
    endOfDay.setHours(23, 59, 59, 999);

    const [trips, total] = await this.tripRepository.findAndCount({
      where: {
        departure,
        destination,
        departureTime: Between(startOfDay, endOfDay),
        availableSeats: MoreThanOrEqual(1),
      },
      relations: ['driver'],
      skip: (page - 1) * limit,
      take: limit,
      order: { departureTime: 'ASC' },
    });

    return { trips, total, page, limit };
  }

  async update(id: string, updateTripDto: UpdateTripDto): Promise<Trip> {
    const trip = await this.findOne(id);

    // Validate driver if provided
    if (updateTripDto.driverId) {
      const driver = await this.driverRepository.findOne({ where: { id: updateTripDto.driverId } });
      if (!driver) {
        throw new NotFoundException('Driver not found');
      }
    }

    // Validate times if provided
    if (updateTripDto.departureTime || updateTripDto.arrivalTime) {
      const departureTime = updateTripDto.departureTime ? new Date(updateTripDto.departureTime) : trip.departureTime;
      const arrivalTime = updateTripDto.arrivalTime ? new Date(updateTripDto.arrivalTime) : trip.arrivalTime;

      if (departureTime >= arrivalTime) {
        throw new BadRequestException('Departure time must be before arrival time');
      }

      if (departureTime <= new Date()) {
        throw new BadRequestException('Departure time must be in the future');
      }
    }

    // Update trip
    Object.assign(trip, updateTripDto);
    if (updateTripDto.departureTime) {
      trip.departureTime = new Date(updateTripDto.departureTime);
    }
    if (updateTripDto.arrivalTime) {
      trip.arrivalTime = new Date(updateTripDto.arrivalTime);
    }

    await this.tripRepository.save(trip);
    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    const trip = await this.findOne(id);

    // Check if trip has any bookings
    const bookedSeats = await this.seatRepository.count({
      where: { tripId: id, isBooked: true },
    });

    if (bookedSeats > 0) {
      throw new BadRequestException('Cannot delete trip with existing bookings');
    }

    await this.tripRepository.remove(trip);
  }
}
