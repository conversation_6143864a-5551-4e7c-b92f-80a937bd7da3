import { Injectable, ConflictException, Logger } from '@nestjs/common';
import { DataSource, EntityManager, OptimisticLockVersionMismatchError } from 'typeorm';

export interface RetryOptions {
  maxRetries?: number;
  baseDelay?: number;
  maxDelay?: number;
  backoffFactor?: number;
}

@Injectable()
export class ConcurrencyService {
  private readonly logger = new Logger(ConcurrencyService.name);

  constructor(private readonly dataSource: DataSource) {}

  /**
   * Executes a function with optimistic locking retry logic
   */
  async withOptimisticRetry<T>(
    operation: (manager: EntityManager) => Promise<T>,
    options: RetryOptions = {}
  ): Promise<T> {
    const {
      maxRetries = 3,
      baseDelay = 100,
      maxDelay = 1000,
      backoffFactor = 2,
    } = options;

    let lastError: Error;
    
    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await this.dataSource.transaction(async (manager) => {
          return await operation(manager);
        });
      } catch (error) {
        lastError = error;
        
        // Check if it's an optimistic lock error
        if (error instanceof OptimisticLockVersionMismatchError || 
            error.message?.includes('version') ||
            error.message?.includes('optimistic')) {
          
          if (attempt < maxRetries) {
            const delay = Math.min(
              baseDelay * Math.pow(backoffFactor, attempt),
              maxDelay
            );
            
            this.logger.warn(
              `Optimistic lock conflict on attempt ${attempt + 1}/${maxRetries + 1}. Retrying in ${delay}ms...`
            );
            
            await this.sleep(delay);
            continue;
          }
        }
        
        // If it's not a retryable error or we've exhausted retries, throw
        throw error;
      }
    }
    
    throw new ConflictException(
      `Operation failed after ${maxRetries + 1} attempts due to concurrent modifications`
    );
  }

  /**
   * Executes a function with pessimistic locking
   */
  async withPessimisticLock<T>(
    operation: (manager: EntityManager) => Promise<T>
  ): Promise<T> {
    return await this.dataSource.transaction(async (manager) => {
      return await operation(manager);
    });
  }

  /**
   * Implements distributed locking using Redis
   */
  async withDistributedLock<T>(
    lockKey: string,
    operation: () => Promise<T>,
    ttlSeconds: number = 30
  ): Promise<T> {
    // This would typically use Redis for distributed locking
    // For now, we'll implement a simple in-memory lock
    const lockId = `${lockKey}:${Date.now()}:${Math.random()}`;
    
    try {
      const acquired = await this.acquireLock(lockKey, lockId, ttlSeconds);
      if (!acquired) {
        throw new ConflictException(`Could not acquire lock for key: ${lockKey}`);
      }
      
      return await operation();
    } finally {
      await this.releaseLock(lockKey, lockId);
    }
  }

  /**
   * Implements circuit breaker pattern for external service calls
   */
  async withCircuitBreaker<T>(
    operation: () => Promise<T>,
    options: {
      failureThreshold?: number;
      resetTimeout?: number;
      monitoringPeriod?: number;
    } = {}
  ): Promise<T> {
    // This is a simplified circuit breaker implementation
    // In production, you'd use a library like opossum
    const {
      failureThreshold = 5,
      resetTimeout = 60000, // 1 minute
      monitoringPeriod = 10000, // 10 seconds
    } = options;

    // Circuit breaker logic would go here
    // For now, just execute the operation
    return await operation();
  }

  private async sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private async acquireLock(key: string, lockId: string, ttlSeconds: number): Promise<boolean> {
    // In a real implementation, this would use Redis SET with NX and EX options
    // For testing purposes, we'll use a simple in-memory approach
    return true;
  }

  private async releaseLock(key: string, lockId: string): Promise<void> {
    // In a real implementation, this would use Redis DEL with Lua script
    // to ensure we only delete our own lock
  }

  /**
   * Implements idempotency key checking
   */
  async withIdempotencyKey<T>(
    idempotencyKey: string,
    operation: () => Promise<T>,
    ttlSeconds: number = 3600 // 1 hour
  ): Promise<T> {
    // Check if we've already processed this request
    const existingResult = await this.getIdempotentResult<T>(idempotencyKey);
    if (existingResult !== null) {
      this.logger.log(`Returning cached result for idempotency key: ${idempotencyKey}`);
      return existingResult;
    }

    // Execute the operation and cache the result
    const result = await operation();
    await this.storeIdempotentResult(idempotencyKey, result, ttlSeconds);
    
    return result;
  }

  private async getIdempotentResult<T>(key: string): Promise<T | null> {
    // In a real implementation, this would use Redis or database
    return null;
  }

  private async storeIdempotentResult<T>(
    key: string, 
    result: T, 
    ttlSeconds: number
  ): Promise<void> {
    // In a real implementation, this would store in Redis or database
  }

  /**
   * Implements saga pattern for distributed transactions
   */
  async executeSaga<T>(
    steps: Array<{
      execute: () => Promise<any>;
      compensate: () => Promise<void>;
    }>
  ): Promise<T> {
    const executedSteps: Array<() => Promise<void>> = [];
    
    try {
      let result: T;
      
      for (const step of steps) {
        const stepResult = await step.execute();
        executedSteps.push(step.compensate);
        
        // Assume the last step returns our final result
        if (steps.indexOf(step) === steps.length - 1) {
          result = stepResult;
        }
      }
      
      return result!;
    } catch (error) {
      // Compensate in reverse order
      this.logger.error('Saga failed, executing compensation steps', error);
      
      for (const compensate of executedSteps.reverse()) {
        try {
          await compensate();
        } catch (compensationError) {
          this.logger.error('Compensation step failed', compensationError);
        }
      }
      
      throw error;
    }
  }
}
