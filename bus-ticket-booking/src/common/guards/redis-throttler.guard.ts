import { Injectable, ExecutionContext } from '@nestjs/common';
import { ThrottlerGuard, ThrottlerModuleOptions } from '@nestjs/throttler';
import { ThrottlerStorage } from '@nestjs/throttler/dist/throttler-storage.interface';
import { Reflector } from '@nestjs/core';
import { RedisService } from '../services/redis.service';

@Injectable()
export class RedisThrottlerGuard extends ThrottlerGuard {
  constructor(
    options: ThrottlerModuleOptions,
    storageService: ThrottlerStorage,
    reflector: Reflector,
    private readonly redisService: RedisService,
  ) {
    super(options, storageService, reflector);
  }

  protected async getTracker(req: Record<string, any>): Promise<string> {
    // Use IP address as the tracker
    return req.ip || req.connection.remoteAddress || 'unknown';
  }

  protected async getHits(context: ExecutionContext): Promise<number> {
    const req = context.switchToHttp().getRequest();
    const tracker = await this.getTracker(req);
    const key = `throttle:${tracker}`;
    
    const hits = await this.redisService.get(key);
    return hits ? parseInt(hits, 10) : 0;
  }

  protected async storageIncrement(
    context: ExecutionContext,
    limit: number,
    ttl: number,
  ): Promise<number> {
    const req = context.switchToHttp().getRequest();
    const tracker = await this.getTracker(req);
    const key = `throttle:${tracker}`;
    
    const current = await this.redisService.incr(key);
    
    if (current === 1) {
      // Set expiration only for the first increment
      await this.redisService.expire(key, ttl);
    }
    
    return current;
  }
}
