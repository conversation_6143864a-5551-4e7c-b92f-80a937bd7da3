{"/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/app.controller.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/app.controller.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 49}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 43}}, "2": {"start": {"line": 5, "column": 7}, "end": {"line": 12, "column": null}}, "3": {"start": {"line": 6, "column": 31}, "end": {"line": 6, "column": 43}}, "4": {"start": {"line": 10, "column": 4}, "end": {"line": 10, "column": 38}}, "5": {"start": {"line": 5, "column": 13}, "end": {"line": 5, "column": 26}}, "6": {"start": {"line": 9, "column": 2}, "end": {"line": 11, "column": null}}, "7": {"start": {"line": 5, "column": 13}, "end": {"line": 12, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 6, "column": 2}, "end": {"line": 6, "column": 31}}, "loc": {"start": {"line": 6, "column": 53}, "end": {"line": 6, "column": 57}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 9, "column": 2}, "end": {"line": 9, "column": 10}}, "loc": {"start": {"line": 9, "column": 10}, "end": {"line": 11, "column": 3}}}}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1}, "f": {"0": 1, "1": 1}, "b": {}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/app.module.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/app.module.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 40}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 61}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 48}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 52}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 49}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 43}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 66}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 48}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 51}}, "9": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 57}}, "10": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 51}}, "11": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 57}}, "12": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 54}}, "13": {"start": {"line": 66, "column": 7}, "end": {"line": 66, "column": null}}, "14": {"start": {"line": 66, "column": 13}, "end": {"line": 66, "column": 22}}, "15": {"start": {"line": 66, "column": 13}, "end": {"line": 66, "column": null}}, "16": {"start": {"line": 28, "column": 25}, "end": {"line": 28, "column": 54}}, "17": {"start": {"line": 29, "column": 8}, "end": {"line": 31, "column": 9}}, "18": {"start": {"line": 30, "column": 10}, "end": {"line": 30, "column": 62}}, "19": {"start": {"line": 32, "column": 8}, "end": {"line": 32, "column": 24}}, "20": {"start": {"line": 40, "column": 53}, "end": {"line": 47, "column": 8}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 27, "column": 18}, "end": {"line": 27, "column": 19}}, "loc": {"start": {"line": 27, "column": 51}, "end": {"line": 33, "column": 7}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 40, "column": 18}, "end": {"line": 40, "column": 19}}, "loc": {"start": {"line": 40, "column": 53}, "end": {"line": 47, "column": 8}}}}, "branchMap": {"0": {"loc": {"start": {"line": 29, "column": 8}, "end": {"line": 31, "column": 9}}, "type": "if", "locations": [{"start": {"line": 29, "column": 8}, "end": {"line": 31, "column": 9}}]}, "1": {"loc": {"start": {"line": 43, "column": 17}, "end": {"line": 43, "column": 63}}, "type": "binary-expr", "locations": [{"start": {"line": 43, "column": 17}, "end": {"line": 43, "column": 54}}, {"start": {"line": 43, "column": 58}, "end": {"line": 43, "column": 63}}]}, "2": {"loc": {"start": {"line": 44, "column": 19}, "end": {"line": 44, "column": 65}}, "type": "binary-expr", "locations": [{"start": {"line": 44, "column": 19}, "end": {"line": 44, "column": 58}}, {"start": {"line": 44, "column": 62}, "end": {"line": 44, "column": 65}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0], "1": [0, 0], "2": [0, 0]}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/app.service.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/app.service.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 44}}, "1": {"start": {"line": 4, "column": 7}, "end": {"line": 8, "column": null}}, "2": {"start": {"line": 6, "column": 4}, "end": {"line": 6, "column": 26}}, "3": {"start": {"line": 4, "column": 13}, "end": {"line": 4, "column": 23}}, "4": {"start": {"line": 4, "column": 13}, "end": {"line": 8, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 5, "column": 2}, "end": {"line": 5, "column": 10}}, "loc": {"start": {"line": 5, "column": 10}, "end": {"line": 7, "column": 3}}}}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1}, "f": {"0": 1}, "b": {}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/main.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/main.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 43}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 56}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 47}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 65}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 41}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": null}}, "6": {"start": {"line": 14, "column": 14}, "end": {"line": 14, "column": 49}}, "7": {"start": {"line": 15, "column": 24}, "end": {"line": 15, "column": 46}}, "8": {"start": {"line": 16, "column": 17}, "end": {"line": 16, "column": 40}}, "9": {"start": {"line": 19, "column": 2}, "end": {"line": 28, "column": 4}}, "10": {"start": {"line": 31, "column": 2}, "end": {"line": 31, "column": 77}}, "11": {"start": {"line": 34, "column": 2}, "end": {"line": 34, "column": 82}}, "12": {"start": {"line": 37, "column": 2}, "end": {"line": 40, "column": 5}}, "13": {"start": {"line": 43, "column": 24}, "end": {"line": 43, "column": 56}}, "14": {"start": {"line": 44, "column": 17}, "end": {"line": 58, "column": 12}}, "15": {"start": {"line": 60, "column": 19}, "end": {"line": 60, "column": 60}}, "16": {"start": {"line": 61, "column": 2}, "end": {"line": 65, "column": 5}}, "17": {"start": {"line": 68, "column": 15}, "end": {"line": 68, "column": 44}}, "18": {"start": {"line": 69, "column": 2}, "end": {"line": 69, "column": 25}}, "19": {"start": {"line": 71, "column": 2}, "end": {"line": 71, "column": 68}}, "20": {"start": {"line": 72, "column": 2}, "end": {"line": 72, "column": 86}}, "21": {"start": {"line": 75, "column": 0}, "end": {"line": 78, "column": 3}}, "22": {"start": {"line": 76, "column": 2}, "end": {"line": 76, "column": 54}}, "23": {"start": {"line": 77, "column": 2}, "end": {"line": 77, "column": 18}}}, "fnMap": {"0": {"name": "bootstrap", "decl": {"start": {"line": 13, "column": 15}, "end": {"line": 13, "column": 24}}, "loc": {"start": {"line": 13, "column": 24}, "end": {"line": 73, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 75, "column": 18}, "end": {"line": 75, "column": 19}}, "loc": {"start": {"line": 75, "column": 28}, "end": {"line": 78, "column": 1}}}}, "branchMap": {"0": {"loc": {"start": {"line": 38, "column": 12}, "end": {"line": 38, "column": 64}}, "type": "cond-expr", "locations": [{"start": {"line": 38, "column": 52}, "end": {"line": 38, "column": 57}}, {"start": {"line": 38, "column": 60}, "end": {"line": 38, "column": 64}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0]}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/admin/admin.module.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/admin/admin.module.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 40}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 48}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 59}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 65}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 43}}, "5": {"start": {"line": 13, "column": 7}, "end": {"line": 13, "column": null}}, "6": {"start": {"line": 13, "column": 13}, "end": {"line": 13, "column": 24}}, "7": {"start": {"line": 13, "column": 13}, "end": {"line": 13, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "f": {}, "b": {}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/admin/drivers/drivers.controller.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/admin/drivers/drivers.controller.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": null}}, "1": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 94}}, "2": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 51}}, "3": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 80}}, "4": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 64}}, "5": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 59}}, "6": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 62}}, "7": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 42}}, "8": {"start": {"line": 26, "column": 7}, "end": {"line": 77, "column": null}}, "9": {"start": {"line": 27, "column": 31}, "end": {"line": 27, "column": 47}}, "10": {"start": {"line": 35, "column": 4}, "end": {"line": 35, "column": 55}}, "11": {"start": {"line": 45, "column": 4}, "end": {"line": 45, "column": 52}}, "12": {"start": {"line": 54, "column": 4}, "end": {"line": 54, "column": 43}}, "13": {"start": {"line": 64, "column": 4}, "end": {"line": 64, "column": 59}}, "14": {"start": {"line": 74, "column": 4}, "end": {"line": 74, "column": 41}}, "15": {"start": {"line": 75, "column": 4}, "end": {"line": 75, "column": 54}}, "16": {"start": {"line": 26, "column": 13}, "end": {"line": 26, "column": 30}}, "17": {"start": {"line": 34, "column": 8}, "end": {"line": 36, "column": null}}, "18": {"start": {"line": 44, "column": 8}, "end": {"line": 46, "column": null}}, "19": {"start": {"line": 53, "column": 8}, "end": {"line": 55, "column": null}}, "20": {"start": {"line": 63, "column": 8}, "end": {"line": 65, "column": null}}, "21": {"start": {"line": 73, "column": 8}, "end": {"line": 76, "column": null}}, "22": {"start": {"line": 26, "column": 13}, "end": {"line": 77, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 27, "column": 2}, "end": {"line": 27, "column": 31}}, "loc": {"start": {"line": 27, "column": 61}, "end": {"line": 27, "column": 65}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 34, "column": 2}, "end": {"line": 34, "column": 7}}, "loc": {"start": {"line": 34, "column": 55}, "end": {"line": 36, "column": 3}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 44, "column": 2}, "end": {"line": 44, "column": 7}}, "loc": {"start": {"line": 44, "column": 76}, "end": {"line": 46, "column": 3}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 53, "column": 2}, "end": {"line": 53, "column": 7}}, "loc": {"start": {"line": 53, "column": 54}, "end": {"line": 55, "column": 3}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 63, "column": 2}, "end": {"line": 63, "column": 7}}, "loc": {"start": {"line": 63, "column": 95}, "end": {"line": 65, "column": 3}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 73, "column": 2}, "end": {"line": 73, "column": 7}}, "loc": {"start": {"line": 73, "column": 53}, "end": {"line": 76, "column": 3}}}}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 1, "17": 1, "18": 1, "19": 1, "20": 1, "21": 1, "22": 1}, "f": {"0": 1, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "b": {}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/admin/drivers/drivers.service.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/admin/drivers/drivers.service.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 103}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 37}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 46}}, "4": {"start": {"line": 8, "column": 7}, "end": {"line": 86, "column": null}}, "5": {"start": {"line": 11, "column": 21}, "end": {"line": 11, "column": 39}}, "6": {"start": {"line": 13, "column": 21}, "end": {"line": 13, "column": 37}}, "7": {"start": {"line": 18, "column": 27}, "end": {"line": 20, "column": 6}}, "8": {"start": {"line": 22, "column": 4}, "end": {"line": 24, "column": 5}}, "9": {"start": {"line": 23, "column": 6}, "end": {"line": 23, "column": 84}}, "10": {"start": {"line": 26, "column": 19}, "end": {"line": 26, "column": 64}}, "11": {"start": {"line": 27, "column": 4}, "end": {"line": 27, "column": 46}}, "12": {"start": {"line": 31, "column": 29}, "end": {"line": 36, "column": 6}}, "13": {"start": {"line": 38, "column": 4}, "end": {"line": 38, "column": 43}}, "14": {"start": {"line": 42, "column": 19}, "end": {"line": 45, "column": 6}}, "15": {"start": {"line": 47, "column": 4}, "end": {"line": 49, "column": 5}}, "16": {"start": {"line": 48, "column": 6}, "end": {"line": 48, "column": 54}}, "17": {"start": {"line": 51, "column": 4}, "end": {"line": 51, "column": 18}}, "18": {"start": {"line": 55, "column": 19}, "end": {"line": 55, "column": 41}}, "19": {"start": {"line": 58, "column": 4}, "end": {"line": 66, "column": 5}}, "20": {"start": {"line": 59, "column": 29}, "end": {"line": 61, "column": 8}}, "21": {"start": {"line": 63, "column": 6}, "end": {"line": 65, "column": 7}}, "22": {"start": {"line": 64, "column": 8}, "end": {"line": 64, "column": 86}}, "23": {"start": {"line": 68, "column": 4}, "end": {"line": 68, "column": 43}}, "24": {"start": {"line": 69, "column": 4}, "end": {"line": 69, "column": 46}}, "25": {"start": {"line": 73, "column": 19}, "end": {"line": 73, "column": 41}}, "26": {"start": {"line": 76, "column": 24}, "end": {"line": 78, "column": 6}}, "27": {"start": {"line": 80, "column": 4}, "end": {"line": 82, "column": 5}}, "28": {"start": {"line": 81, "column": 6}, "end": {"line": 81, "column": 78}}, "29": {"start": {"line": 84, "column": 4}, "end": {"line": 84, "column": 47}}, "30": {"start": {"line": 8, "column": 13}, "end": {"line": 8, "column": 27}}, "31": {"start": {"line": 8, "column": 13}, "end": {"line": 86, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 9, "column": 2}, "end": {"line": 9, "column": null}}, "loc": {"start": {"line": 13, "column": 53}, "end": {"line": 14, "column": 6}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 16, "column": 2}, "end": {"line": 16, "column": 7}}, "loc": {"start": {"line": 16, "column": 47}, "end": {"line": 28, "column": 3}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 30, "column": 2}, "end": {"line": 30, "column": 7}}, "loc": {"start": {"line": 30, "column": 52}, "end": {"line": 39, "column": 3}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 41, "column": 2}, "end": {"line": 41, "column": 7}}, "loc": {"start": {"line": 41, "column": 26}, "end": {"line": 52, "column": 3}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 54, "column": 2}, "end": {"line": 54, "column": 7}}, "loc": {"start": {"line": 54, "column": 59}, "end": {"line": 70, "column": 3}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 72, "column": 2}, "end": {"line": 72, "column": 7}}, "loc": {"start": {"line": 72, "column": 25}, "end": {"line": 85, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 22, "column": 4}, "end": {"line": 24, "column": 5}}, "type": "if", "locations": [{"start": {"line": 22, "column": 4}, "end": {"line": 24, "column": 5}}]}, "1": {"loc": {"start": {"line": 30, "column": 16}, "end": {"line": 30, "column": 32}}, "type": "default-arg", "locations": [{"start": {"line": 30, "column": 31}, "end": {"line": 30, "column": 32}}]}, "2": {"loc": {"start": {"line": 30, "column": 34}, "end": {"line": 30, "column": 52}}, "type": "default-arg", "locations": [{"start": {"line": 30, "column": 50}, "end": {"line": 30, "column": 52}}]}, "3": {"loc": {"start": {"line": 47, "column": 4}, "end": {"line": 49, "column": 5}}, "type": "if", "locations": [{"start": {"line": 47, "column": 4}, "end": {"line": 49, "column": 5}}]}, "4": {"loc": {"start": {"line": 58, "column": 4}, "end": {"line": 66, "column": 5}}, "type": "if", "locations": [{"start": {"line": 58, "column": 4}, "end": {"line": 66, "column": 5}}]}, "5": {"loc": {"start": {"line": 58, "column": 8}, "end": {"line": 58, "column": 95}}, "type": "binary-expr", "locations": [{"start": {"line": 58, "column": 8}, "end": {"line": 58, "column": 37}}, {"start": {"line": 58, "column": 41}, "end": {"line": 58, "column": 95}}]}, "6": {"loc": {"start": {"line": 63, "column": 6}, "end": {"line": 65, "column": 7}}, "type": "if", "locations": [{"start": {"line": 63, "column": 6}, "end": {"line": 65, "column": 7}}]}, "7": {"loc": {"start": {"line": 80, "column": 4}, "end": {"line": 82, "column": 5}}, "type": "if", "locations": [{"start": {"line": 80, "column": 4}, "end": {"line": 82, "column": 5}}]}}, "s": {"0": 2, "1": 2, "2": 2, "3": 2, "4": 2, "5": 1, "6": 1, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 2, "31": 2}, "f": {"0": 1, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0], "4": [0], "5": [0, 0], "6": [0], "7": [0]}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/auth/auth.controller.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/auth/auth.controller.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 78}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 69}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 45}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 64}}, "4": {"start": {"line": 8, "column": 7}, "end": {"line": 27, "column": null}}, "5": {"start": {"line": 9, "column": 31}, "end": {"line": 9, "column": 44}}, "6": {"start": {"line": 16, "column": 4}, "end": {"line": 16, "column": 50}}, "7": {"start": {"line": 25, "column": 4}, "end": {"line": 25, "column": 44}}, "8": {"start": {"line": 8, "column": 13}, "end": {"line": 8, "column": 27}}, "9": {"start": {"line": 15, "column": 8}, "end": {"line": 17, "column": null}}, "10": {"start": {"line": 24, "column": 8}, "end": {"line": 26, "column": null}}, "11": {"start": {"line": 8, "column": 13}, "end": {"line": 27, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 9, "column": 2}, "end": {"line": 9, "column": 31}}, "loc": {"start": {"line": 9, "column": 55}, "end": {"line": 9, "column": 59}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": 7}}, "loc": {"start": {"line": 15, "column": 49}, "end": {"line": 17, "column": 3}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 24, "column": 2}, "end": {"line": 24, "column": 7}}, "loc": {"start": {"line": 24, "column": 40}, "end": {"line": 26, "column": 3}}}}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 2, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 1}, "f": {"0": 2, "1": 1, "2": 1}, "b": {}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/auth/auth.module.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/auth/auth.module.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 40}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 40}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 50}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 48}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 61}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 45}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 51}}, "7": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 56}}, "8": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 35}}, "9": {"start": {"line": 30, "column": 7}, "end": {"line": 30, "column": null}}, "10": {"start": {"line": 30, "column": 13}, "end": {"line": 30, "column": 23}}, "11": {"start": {"line": 30, "column": 13}, "end": {"line": 30, "column": null}}, "12": {"start": {"line": 17, "column": 59}, "end": {"line": 22, "column": 8}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 17, "column": 18}, "end": {"line": 17, "column": 23}}, "loc": {"start": {"line": 17, "column": 59}, "end": {"line": 22, "column": 8}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0}, "f": {"0": 0}, "b": {}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/auth/auth.service.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/auth/auth.service.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 86}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 41}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 51}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 37}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 35}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 45}}, "6": {"start": {"line": 10, "column": 7}, "end": {"line": 91, "column": null}}, "7": {"start": {"line": 13, "column": 21}, "end": {"line": 13, "column": 37}}, "8": {"start": {"line": 14, "column": 21}, "end": {"line": 14, "column": 33}}, "9": {"start": {"line": 18, "column": 53}, "end": {"line": 18, "column": 64}}, "10": {"start": {"line": 21, "column": 25}, "end": {"line": 21, "column": 80}}, "11": {"start": {"line": 22, "column": 4}, "end": {"line": 24, "column": 5}}, "12": {"start": {"line": 23, "column": 6}, "end": {"line": 23, "column": 73}}, "13": {"start": {"line": 27, "column": 23}, "end": {"line": 27, "column": 25}}, "14": {"start": {"line": 28, "column": 27}, "end": {"line": 28, "column": 66}}, "15": {"start": {"line": 31, "column": 17}, "end": {"line": 37, "column": 6}}, "16": {"start": {"line": 39, "column": 22}, "end": {"line": 39, "column": 58}}, "17": {"start": {"line": 42, "column": 20}, "end": {"line": 42, "column": 87}}, "18": {"start": {"line": 43, "column": 25}, "end": {"line": 43, "column": 54}}, "19": {"start": {"line": 45, "column": 4}, "end": {"line": 54, "column": 6}}, "20": {"start": {"line": 58, "column": 32}, "end": {"line": 58, "column": 40}}, "21": {"start": {"line": 61, "column": 17}, "end": {"line": 61, "column": 72}}, "22": {"start": {"line": 62, "column": 4}, "end": {"line": 64, "column": 5}}, "23": {"start": {"line": 63, "column": 6}, "end": {"line": 63, "column": 61}}, "24": {"start": {"line": 67, "column": 28}, "end": {"line": 67, "column": 73}}, "25": {"start": {"line": 68, "column": 4}, "end": {"line": 70, "column": 5}}, "26": {"start": {"line": 69, "column": 6}, "end": {"line": 69, "column": 61}}, "27": {"start": {"line": 73, "column": 20}, "end": {"line": 73, "column": 72}}, "28": {"start": {"line": 74, "column": 25}, "end": {"line": 74, "column": 54}}, "29": {"start": {"line": 76, "column": 4}, "end": {"line": 85, "column": 6}}, "30": {"start": {"line": 89, "column": 4}, "end": {"line": 89, "column": 66}}, "31": {"start": {"line": 10, "column": 13}, "end": {"line": 10, "column": 24}}, "32": {"start": {"line": 10, "column": 13}, "end": {"line": 91, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 11, "column": 2}, "end": {"line": 11, "column": null}}, "loc": {"start": {"line": 14, "column": 43}, "end": {"line": 15, "column": 6}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 17, "column": 2}, "end": {"line": 17, "column": 7}}, "loc": {"start": {"line": 17, "column": 41}, "end": {"line": 55, "column": 3}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 57, "column": 2}, "end": {"line": 57, "column": 7}}, "loc": {"start": {"line": 57, "column": 32}, "end": {"line": 86, "column": 3}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 88, "column": 2}, "end": {"line": 88, "column": 7}}, "loc": {"start": {"line": 88, "column": 35}, "end": {"line": 90, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 22, "column": 4}, "end": {"line": 24, "column": 5}}, "type": "if", "locations": [{"start": {"line": 22, "column": 4}, "end": {"line": 24, "column": 5}}]}, "1": {"loc": {"start": {"line": 62, "column": 4}, "end": {"line": 64, "column": 5}}, "type": "if", "locations": [{"start": {"line": 62, "column": 4}, "end": {"line": 64, "column": 5}}]}, "2": {"loc": {"start": {"line": 68, "column": 4}, "end": {"line": 70, "column": 5}}, "type": "if", "locations": [{"start": {"line": 68, "column": 4}, "end": {"line": 70, "column": 5}}]}}, "s": {"0": 3, "1": 3, "2": 3, "3": 3, "4": 3, "5": 3, "6": 3, "7": 7, "8": 7, "9": 2, "10": 2, "11": 2, "12": 1, "13": 1, "14": 1, "15": 1, "16": 1, "17": 1, "18": 1, "19": 1, "20": 3, "21": 3, "22": 3, "23": 1, "24": 2, "25": 2, "26": 1, "27": 1, "28": 1, "29": 1, "30": 2, "31": 3, "32": 3}, "f": {"0": 7, "1": 2, "2": 3, "3": 2}, "b": {"0": [1], "1": [1], "2": [1]}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/auth/decorators/current-user.decorator.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/auth/decorators/current-user.decorator.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 72}}, "1": {"start": {"line": 4, "column": 13}, "end": {"line": 9, "column": 2}}, "2": {"start": {"line": 6, "column": 20}, "end": {"line": 6, "column": 51}}, "3": {"start": {"line": 7, "column": 4}, "end": {"line": 7, "column": 24}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 5, "column": 2}, "end": {"line": 5, "column": 3}}, "loc": {"start": {"line": 5, "column": 49}, "end": {"line": 8, "column": 3}}}}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 0, "3": 0}, "f": {"0": 0}, "b": {}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/auth/decorators/index.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/auth/decorators/index.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 41}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 34}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0}, "f": {}, "b": {}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/auth/decorators/roles.decorator.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/auth/decorators/roles.decorator.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 45}}, "1": {"start": {"line": 4, "column": 21}, "end": {"line": 4, "column": 74}}, "2": {"start": {"line": 4, "column": 47}, "end": {"line": 4, "column": 74}}, "3": {"start": {"line": 4, "column": 13}, "end": {"line": 4, "column": 21}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 4, "column": 21}, "end": {"line": 4, "column": 22}}, "loc": {"start": {"line": 4, "column": 47}, "end": {"line": 4, "column": 74}}}}, "branchMap": {}, "s": {"0": 3, "1": 3, "2": 6, "3": 3}, "f": {"0": 6}, "b": {}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/auth/guards/jwt-auth.guard.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/auth/guards/jwt-auth.guard.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 44}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 45}}, "2": {"start": {"line": 5, "column": 7}, "end": {"line": 5, "column": null}}, "3": {"start": {"line": 5, "column": 13}, "end": {"line": 5, "column": 25}}, "4": {"start": {"line": 5, "column": 13}, "end": {"line": 5, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 3, "1": 3, "2": 3, "3": 3, "4": 3}, "f": {}, "b": {}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/auth/guards/roles.guard.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/auth/guards/roles.guard.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 75}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 41}}, "2": {"start": {"line": 6, "column": 7}, "end": {"line": 22, "column": null}}, "3": {"start": {"line": 7, "column": 22}, "end": {"line": 7, "column": 33}}, "4": {"start": {"line": 10, "column": 26}, "end": {"line": 13, "column": 6}}, "5": {"start": {"line": 15, "column": 4}, "end": {"line": 17, "column": 5}}, "6": {"start": {"line": 16, "column": 6}, "end": {"line": 16, "column": 18}}, "7": {"start": {"line": 19, "column": 21}, "end": {"line": 19, "column": 56}}, "8": {"start": {"line": 20, "column": 4}, "end": {"line": 20, "column": 61}}, "9": {"start": {"line": 20, "column": 40}, "end": {"line": 20, "column": 59}}, "10": {"start": {"line": 6, "column": 13}, "end": {"line": 6, "column": 23}}, "11": {"start": {"line": 6, "column": 13}, "end": {"line": 22, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 7, "column": 2}, "end": {"line": 7, "column": 22}}, "loc": {"start": {"line": 7, "column": 42}, "end": {"line": 7, "column": 46}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 9, "column": 2}, "end": {"line": 9, "column": 13}}, "loc": {"start": {"line": 9, "column": 39}, "end": {"line": 21, "column": 3}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 20, "column": 30}, "end": {"line": 20, "column": 31}}, "loc": {"start": {"line": 20, "column": 40}, "end": {"line": 20, "column": 59}}}}, "branchMap": {"0": {"loc": {"start": {"line": 15, "column": 4}, "end": {"line": 17, "column": 5}}, "type": "if", "locations": [{"start": {"line": 15, "column": 4}, "end": {"line": 17, "column": 5}}]}}, "s": {"0": 4, "1": 4, "2": 4, "3": 8, "4": 5, "5": 5, "6": 1, "7": 4, "8": 4, "9": 5, "10": 4, "11": 4}, "f": {"0": 8, "1": 5, "2": 5}, "b": {"0": [1]}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/auth/strategies/jwt.strategy.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/auth/strategies/jwt.strategy.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 67}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 52}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 52}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 47}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 46}}, "5": {"start": {"line": 16, "column": 7}, "end": {"line": 35, "column": null}}, "6": {"start": {"line": 21, "column": 4}, "end": {"line": 25, "column": 7}}, "7": {"start": {"line": 18, "column": 21}, "end": {"line": 18, "column": 36}}, "8": {"start": {"line": 19, "column": 21}, "end": {"line": 19, "column": 34}}, "9": {"start": {"line": 29, "column": 17}, "end": {"line": 29, "column": 65}}, "10": {"start": {"line": 30, "column": 4}, "end": {"line": 32, "column": 5}}, "11": {"start": {"line": 31, "column": 6}, "end": {"line": 31, "column": 56}}, "12": {"start": {"line": 33, "column": 4}, "end": {"line": 33, "column": 16}}, "13": {"start": {"line": 16, "column": 13}, "end": {"line": 16, "column": 24}}, "14": {"start": {"line": 16, "column": 13}, "end": {"line": 35, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 17, "column": 2}, "end": {"line": 17, "column": null}}, "loc": {"start": {"line": 19, "column": 45}, "end": {"line": 26, "column": 3}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 28, "column": 2}, "end": {"line": 28, "column": 7}}, "loc": {"start": {"line": 28, "column": 36}, "end": {"line": 34, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 24, "column": 19}, "end": {"line": 24, "column": 82}}, "type": "binary-expr", "locations": [{"start": {"line": 24, "column": 19}, "end": {"line": 24, "column": 62}}, {"start": {"line": 24, "column": 66}, "end": {"line": 24, "column": 82}}]}, "1": {"loc": {"start": {"line": 30, "column": 4}, "end": {"line": 32, "column": 5}}, "type": "if", "locations": [{"start": {"line": 30, "column": 4}, "end": {"line": 32, "column": 5}}]}}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 2, "7": 2, "8": 2, "9": 2, "10": 2, "11": 1, "12": 1, "13": 1, "14": 1}, "f": {"0": 2, "1": 2}, "b": {"0": [2, 0], "1": [1]}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/common/common.module.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/common/common.module.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 48}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 56}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 68}}, "3": {"start": {"line": 10, "column": 7}, "end": {"line": 10, "column": null}}, "4": {"start": {"line": 10, "column": 13}, "end": {"line": 10, "column": 25}}, "5": {"start": {"line": 10, "column": 13}, "end": {"line": 10, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "f": {}, "b": {}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/common/index.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/common/index.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 48}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 48}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 51}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 53}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 41}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 47}}, "6": {"start": {"line": 7, "column": 0}, "end": {"line": 7, "column": 47}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "f": {}, "b": {}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/common/filters/all-exceptions.filter.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/common/filters/all-exceptions.filter.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": null}}, "1": {"start": {"line": 12, "column": 32}, "end": {"line": 46, "column": null}}, "2": {"start": {"line": 13, "column": 28}, "end": {"line": 13, "column": 64}}, "3": {"start": {"line": 16, "column": 16}, "end": {"line": 16, "column": 35}}, "4": {"start": {"line": 17, "column": 21}, "end": {"line": 17, "column": 48}}, "5": {"start": {"line": 18, "column": 20}, "end": {"line": 18, "column": 45}}, "6": {"start": {"line": 20, "column": 17}, "end": {"line": 20, "column": 49}}, "7": {"start": {"line": 21, "column": 18}, "end": {"line": 21, "column": 41}}, "8": {"start": {"line": 23, "column": 4}, "end": {"line": 28, "column": 5}}, "9": {"start": {"line": 24, "column": 6}, "end": {"line": 24, "column": 37}}, "10": {"start": {"line": 25, "column": 6}, "end": {"line": 25, "column": 34}}, "11": {"start": {"line": 26, "column": 11}, "end": {"line": 28, "column": 5}}, "12": {"start": {"line": 27, "column": 6}, "end": {"line": 27, "column": 34}}, "13": {"start": {"line": 30, "column": 26}, "end": {"line": 36, "column": 6}}, "14": {"start": {"line": 39, "column": 4}, "end": {"line": 42, "column": 6}}, "15": {"start": {"line": 44, "column": 4}, "end": {"line": 44, "column": 48}}, "16": {"start": {"line": 12, "column": 13}, "end": {"line": 12, "column": 32}}, "17": {"start": {"line": 12, "column": 13}, "end": {"line": 46, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": 7}}, "loc": {"start": {"line": 15, "column": 47}, "end": {"line": 45, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 23, "column": 4}, "end": {"line": 28, "column": 5}}, "type": "if", "locations": [{"start": {"line": 23, "column": 4}, "end": {"line": 28, "column": 5}}, {"start": {"line": 26, "column": 11}, "end": {"line": 28, "column": 5}}]}, "1": {"loc": {"start": {"line": 26, "column": 11}, "end": {"line": 28, "column": 5}}, "type": "if", "locations": [{"start": {"line": 26, "column": 11}, "end": {"line": 28, "column": 5}}]}, "2": {"loc": {"start": {"line": 41, "column": 6}, "end": {"line": 41, "column": 62}}, "type": "cond-expr", "locations": [{"start": {"line": 41, "column": 35}, "end": {"line": 41, "column": 50}}, {"start": {"line": 41, "column": 53}, "end": {"line": 41, "column": 62}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0}, "f": {"0": 0}, "b": {"0": [0, 0], "1": [0], "2": [0, 0]}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/common/filters/http-exception.filter.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/common/filters/http-exception.filter.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": null}}, "1": {"start": {"line": 12, "column": 32}, "end": {"line": 45, "column": null}}, "2": {"start": {"line": 13, "column": 28}, "end": {"line": 13, "column": 64}}, "3": {"start": {"line": 16, "column": 16}, "end": {"line": 16, "column": 35}}, "4": {"start": {"line": 17, "column": 21}, "end": {"line": 17, "column": 48}}, "5": {"start": {"line": 18, "column": 20}, "end": {"line": 18, "column": 45}}, "6": {"start": {"line": 19, "column": 19}, "end": {"line": 19, "column": 40}}, "7": {"start": {"line": 21, "column": 26}, "end": {"line": 27, "column": 6}}, "8": {"start": {"line": 30, "column": 4}, "end": {"line": 35, "column": 5}}, "9": {"start": {"line": 31, "column": 32}, "end": {"line": 31, "column": 55}}, "10": {"start": {"line": 32, "column": 6}, "end": {"line": 34, "column": 7}}, "11": {"start": {"line": 33, "column": 8}, "end": {"line": 33, "column": 61}}, "12": {"start": {"line": 38, "column": 4}, "end": {"line": 41, "column": 6}}, "13": {"start": {"line": 43, "column": 4}, "end": {"line": 43, "column": 48}}, "14": {"start": {"line": 12, "column": 13}, "end": {"line": 12, "column": 32}}, "15": {"start": {"line": 12, "column": 13}, "end": {"line": 45, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": 7}}, "loc": {"start": {"line": 15, "column": 53}, "end": {"line": 44, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 26, "column": 15}, "end": {"line": 26, "column": 59}}, "type": "binary-expr", "locations": [{"start": {"line": 26, "column": 15}, "end": {"line": 26, "column": 32}}, {"start": {"line": 26, "column": 36}, "end": {"line": 26, "column": 59}}]}, "1": {"loc": {"start": {"line": 30, "column": 4}, "end": {"line": 35, "column": 5}}, "type": "if", "locations": [{"start": {"line": 30, "column": 4}, "end": {"line": 35, "column": 5}}]}, "2": {"loc": {"start": {"line": 32, "column": 6}, "end": {"line": 34, "column": 7}}, "type": "if", "locations": [{"start": {"line": 32, "column": 6}, "end": {"line": 34, "column": 7}}]}, "3": {"loc": {"start": {"line": 32, "column": 10}, "end": {"line": 32, "column": 79}}, "type": "binary-expr", "locations": [{"start": {"line": 32, "column": 10}, "end": {"line": 32, "column": 47}}, {"start": {"line": 32, "column": 51}, "end": {"line": 32, "column": 79}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0}, "f": {"0": 0}, "b": {"0": [0, 0], "1": [0], "2": [0], "3": [0, 0]}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/common/guards/redis-throttler.guard.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/common/guards/redis-throttler.guard.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 62}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 75}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 86}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 41}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 57}}, "5": {"start": {"line": 8, "column": 7}, "end": {"line": 50, "column": null}}, "6": {"start": {"line": 15, "column": 4}, "end": {"line": 15, "column": 46}}, "7": {"start": {"line": 13, "column": 21}, "end": {"line": 13, "column": 35}}, "8": {"start": {"line": 20, "column": 4}, "end": {"line": 20, "column": 63}}, "9": {"start": {"line": 24, "column": 16}, "end": {"line": 24, "column": 51}}, "10": {"start": {"line": 25, "column": 20}, "end": {"line": 25, "column": 46}}, "11": {"start": {"line": 26, "column": 16}, "end": {"line": 26, "column": 37}}, "12": {"start": {"line": 28, "column": 17}, "end": {"line": 28, "column": 49}}, "13": {"start": {"line": 29, "column": 4}, "end": {"line": 29, "column": 41}}, "14": {"start": {"line": 37, "column": 16}, "end": {"line": 37, "column": 51}}, "15": {"start": {"line": 38, "column": 20}, "end": {"line": 38, "column": 46}}, "16": {"start": {"line": 39, "column": 16}, "end": {"line": 39, "column": 37}}, "17": {"start": {"line": 41, "column": 20}, "end": {"line": 41, "column": 53}}, "18": {"start": {"line": 43, "column": 4}, "end": {"line": 46, "column": 5}}, "19": {"start": {"line": 45, "column": 6}, "end": {"line": 45, "column": 47}}, "20": {"start": {"line": 48, "column": 4}, "end": {"line": 48, "column": 19}}, "21": {"start": {"line": 8, "column": 13}, "end": {"line": 8, "column": 32}}, "22": {"start": {"line": 8, "column": 13}, "end": {"line": 50, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 9, "column": 2}, "end": {"line": 9, "column": null}}, "loc": {"start": {"line": 13, "column": 47}, "end": {"line": 16, "column": 3}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 18, "column": 12}, "end": {"line": 18, "column": 17}}, "loc": {"start": {"line": 18, "column": 53}, "end": {"line": 21, "column": 3}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 23, "column": 12}, "end": {"line": 23, "column": 17}}, "loc": {"start": {"line": 23, "column": 51}, "end": {"line": 30, "column": 3}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 32, "column": 12}, "end": {"line": 32, "column": 17}}, "loc": {"start": {"line": 35, "column": 15}, "end": {"line": 49, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 20, "column": 11}, "end": {"line": 20, "column": 62}}, "type": "binary-expr", "locations": [{"start": {"line": 20, "column": 11}, "end": {"line": 20, "column": 17}}, {"start": {"line": 20, "column": 21}, "end": {"line": 20, "column": 49}}, {"start": {"line": 20, "column": 53}, "end": {"line": 20, "column": 62}}]}, "1": {"loc": {"start": {"line": 29, "column": 11}, "end": {"line": 29, "column": 40}}, "type": "cond-expr", "locations": [{"start": {"line": 29, "column": 18}, "end": {"line": 29, "column": 36}}, {"start": {"line": 29, "column": 39}, "end": {"line": 29, "column": 40}}]}, "2": {"loc": {"start": {"line": 43, "column": 4}, "end": {"line": 46, "column": 5}}, "type": "if", "locations": [{"start": {"line": 43, "column": 4}, "end": {"line": 46, "column": 5}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {"0": [0, 0, 0], "1": [0, 0], "2": [0]}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/common/interceptors/logging.interceptor.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/common/interceptors/logging.interceptor.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": null}}, "1": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 37}}, "2": {"start": {"line": 13, "column": 31}, "end": {"line": 41, "column": null}}, "3": {"start": {"line": 14, "column": 28}, "end": {"line": 14, "column": 63}}, "4": {"start": {"line": 17, "column": 16}, "end": {"line": 17, "column": 38}}, "5": {"start": {"line": 18, "column": 20}, "end": {"line": 18, "column": 45}}, "6": {"start": {"line": 19, "column": 21}, "end": {"line": 19, "column": 48}}, "7": {"start": {"line": 20, "column": 32}, "end": {"line": 20, "column": 39}}, "8": {"start": {"line": 21, "column": 22}, "end": {"line": 21, "column": 53}}, "9": {"start": {"line": 23, "column": 16}, "end": {"line": 23, "column": 26}}, "10": {"start": {"line": 25, "column": 4}, "end": {"line": 27, "column": 6}}, "11": {"start": {"line": 29, "column": 4}, "end": {"line": 39, "column": 6}}, "12": {"start": {"line": 31, "column": 31}, "end": {"line": 31, "column": 39}}, "13": {"start": {"line": 32, "column": 30}, "end": {"line": 32, "column": 60}}, "14": {"start": {"line": 33, "column": 29}, "end": {"line": 33, "column": 45}}, "15": {"start": {"line": 35, "column": 8}, "end": {"line": 37, "column": 10}}, "16": {"start": {"line": 13, "column": 13}, "end": {"line": 13, "column": 31}}, "17": {"start": {"line": 13, "column": 13}, "end": {"line": 41, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 16, "column": 2}, "end": {"line": 16, "column": 11}}, "loc": {"start": {"line": 16, "column": 56}, "end": {"line": 40, "column": 3}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 30, "column": 10}, "end": {"line": 30, "column": 13}}, "loc": {"start": {"line": 30, "column": 15}, "end": {"line": 38, "column": 7}}}}, "branchMap": {"0": {"loc": {"start": {"line": 21, "column": 22}, "end": {"line": 21, "column": 53}}, "type": "binary-expr", "locations": [{"start": {"line": 21, "column": 22}, "end": {"line": 21, "column": 47}}, {"start": {"line": 21, "column": 51}, "end": {"line": 21, "column": 53}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0}, "f": {"0": 0, "1": 0}, "b": {"0": [0, 0]}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/common/interceptors/transform.interceptor.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/common/interceptors/transform.interceptor.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": null}}, "1": {"start": {"line": 8, "column": 0}, "end": {"line": 8, "column": 37}}, "2": {"start": {"line": 9, "column": 0}, "end": {"line": 9, "column": 49}}, "3": {"start": {"line": 19, "column": 7}, "end": {"line": 38, "column": null}}, "4": {"start": {"line": 26, "column": 16}, "end": {"line": 26, "column": 38}}, "5": {"start": {"line": 27, "column": 21}, "end": {"line": 27, "column": 38}}, "6": {"start": {"line": 29, "column": 4}, "end": {"line": 36, "column": 6}}, "7": {"start": {"line": 30, "column": 24}, "end": {"line": 35, "column": 8}}, "8": {"start": {"line": 19, "column": 13}, "end": {"line": 19, "column": 33}}, "9": {"start": {"line": 19, "column": 13}, "end": {"line": 38, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_1)", "decl": {"start": {"line": 22, "column": 2}, "end": {"line": 22, "column": 11}}, "loc": {"start": {"line": 24, "column": 21}, "end": {"line": 37, "column": 3}}}, "1": {"name": "(anonymous_2)", "decl": {"start": {"line": 30, "column": 10}, "end": {"line": 30, "column": 11}}, "loc": {"start": {"line": 30, "column": 24}, "end": {"line": 35, "column": 8}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/common/services/concurrency.service.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/common/services/concurrency.service.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 71}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 88}}, "2": {"start": {"line": 12, "column": 31}, "end": {"line": 220, "column": null}}, "3": {"start": {"line": 13, "column": 28}, "end": {"line": 13, "column": 63}}, "4": {"start": {"line": 15, "column": 31}, "end": {"line": 15, "column": 43}}, "5": {"start": {"line": 29, "column": 8}, "end": {"line": 29, "column": 15}}, "6": {"start": {"line": 33, "column": 4}, "end": {"line": 64, "column": 5}}, "7": {"start": {"line": 33, "column": 23}, "end": {"line": 33, "column": 24}}, "8": {"start": {"line": 34, "column": 6}, "end": {"line": 63, "column": 7}}, "9": {"start": {"line": 35, "column": 8}, "end": {"line": 37, "column": 11}}, "10": {"start": {"line": 36, "column": 10}, "end": {"line": 36, "column": 42}}, "11": {"start": {"line": 39, "column": 8}, "end": {"line": 39, "column": 26}}, "12": {"start": {"line": 42, "column": 8}, "end": {"line": 59, "column": 9}}, "13": {"start": {"line": 46, "column": 10}, "end": {"line": 58, "column": 11}}, "14": {"start": {"line": 47, "column": 26}, "end": {"line": 49, "column": null}}, "15": {"start": {"line": 52, "column": 12}, "end": {"line": 54, "column": 14}}, "16": {"start": {"line": 56, "column": 12}, "end": {"line": 56, "column": 36}}, "17": {"start": {"line": 57, "column": 12}, "end": {"line": 57, "column": 21}}, "18": {"start": {"line": 62, "column": 8}, "end": {"line": 62, "column": 20}}, "19": {"start": {"line": 66, "column": 4}, "end": {"line": 68, "column": 6}}, "20": {"start": {"line": 77, "column": 4}, "end": {"line": 79, "column": 7}}, "21": {"start": {"line": 78, "column": 6}, "end": {"line": 78, "column": 38}}, "22": {"start": {"line": 92, "column": 19}, "end": {"line": 92, "column": 62}}, "23": {"start": {"line": 94, "column": 4}, "end": {"line": 103, "column": 5}}, "24": {"start": {"line": 95, "column": 23}, "end": {"line": 95, "column": 74}}, "25": {"start": {"line": 96, "column": 6}, "end": {"line": 98, "column": 7}}, "26": {"start": {"line": 97, "column": 8}, "end": {"line": 97, "column": 82}}, "27": {"start": {"line": 100, "column": 6}, "end": {"line": 100, "column": 31}}, "28": {"start": {"line": 102, "column": 6}, "end": {"line": 102, "column": 46}}, "29": {"start": {"line": 123, "column": 8}, "end": {"line": 123, "column": 15}}, "30": {"start": {"line": 127, "column": 4}, "end": {"line": 127, "column": 29}}, "31": {"start": {"line": 131, "column": 4}, "end": {"line": 131, "column": 59}}, "32": {"start": {"line": 131, "column": 34}, "end": {"line": 131, "column": 57}}, "33": {"start": {"line": 137, "column": 4}, "end": {"line": 137, "column": 16}}, "34": {"start": {"line": 154, "column": 27}, "end": {"line": 154, "column": 76}}, "35": {"start": {"line": 155, "column": 4}, "end": {"line": 158, "column": 5}}, "36": {"start": {"line": 156, "column": 6}, "end": {"line": 156, "column": 88}}, "37": {"start": {"line": 157, "column": 6}, "end": {"line": 157, "column": 28}}, "38": {"start": {"line": 161, "column": 19}, "end": {"line": 161, "column": 36}}, "39": {"start": {"line": 162, "column": 4}, "end": {"line": 162, "column": 73}}, "40": {"start": {"line": 164, "column": 4}, "end": {"line": 164, "column": 18}}, "41": {"start": {"line": 169, "column": 4}, "end": {"line": 169, "column": 16}}, "42": {"start": {"line": 189, "column": 54}, "end": {"line": 189, "column": 56}}, "43": {"start": {"line": 191, "column": 4}, "end": {"line": 218, "column": 5}}, "44": {"start": {"line": 194, "column": 6}, "end": {"line": 202, "column": 7}}, "45": {"start": {"line": 195, "column": 27}, "end": {"line": 195, "column": 47}}, "46": {"start": {"line": 196, "column": 8}, "end": {"line": 196, "column": 44}}, "47": {"start": {"line": 199, "column": 8}, "end": {"line": 201, "column": 9}}, "48": {"start": {"line": 200, "column": 10}, "end": {"line": 200, "column": 30}}, "49": {"start": {"line": 204, "column": 6}, "end": {"line": 204, "column": 21}}, "50": {"start": {"line": 207, "column": 6}, "end": {"line": 207, "column": 76}}, "51": {"start": {"line": 209, "column": 6}, "end": {"line": 215, "column": 7}}, "52": {"start": {"line": 210, "column": 8}, "end": {"line": 214, "column": 9}}, "53": {"start": {"line": 211, "column": 10}, "end": {"line": 211, "column": 29}}, "54": {"start": {"line": 213, "column": 10}, "end": {"line": 213, "column": 75}}, "55": {"start": {"line": 217, "column": 6}, "end": {"line": 217, "column": 18}}, "56": {"start": {"line": 12, "column": 13}, "end": {"line": 12, "column": 31}}, "57": {"start": {"line": 12, "column": 13}, "end": {"line": 220, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": 31}}, "loc": {"start": {"line": 15, "column": 53}, "end": {"line": 15, "column": 57}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 20, "column": 2}, "end": {"line": 20, "column": 7}}, "loc": {"start": {"line": 22, "column": 30}, "end": {"line": 69, "column": 3}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 35, "column": 49}, "end": {"line": 35, "column": 54}}, "loc": {"start": {"line": 35, "column": 67}, "end": {"line": 37, "column": 9}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 74, "column": 2}, "end": {"line": 74, "column": 7}}, "loc": {"start": {"line": 75, "column": 53}, "end": {"line": 80, "column": 3}}}, "4": {"name": "(anonymous_6)", "decl": {"start": {"line": 77, "column": 45}, "end": {"line": 77, "column": 50}}, "loc": {"start": {"line": 77, "column": 63}, "end": {"line": 79, "column": 5}}}, "5": {"name": "(anonymous_7)", "decl": {"start": {"line": 85, "column": 2}, "end": {"line": 85, "column": 7}}, "loc": {"start": {"line": 88, "column": 27}, "end": {"line": 104, "column": 3}}}, "6": {"name": "(anonymous_8)", "decl": {"start": {"line": 109, "column": 2}, "end": {"line": 109, "column": 7}}, "loc": {"start": {"line": 115, "column": 10}, "end": {"line": 128, "column": 3}}}, "7": {"name": "(anonymous_9)", "decl": {"start": {"line": 130, "column": 10}, "end": {"line": 130, "column": 15}}, "loc": {"start": {"line": 130, "column": 32}, "end": {"line": 132, "column": 3}}}, "8": {"name": "(anonymous_10)", "decl": {"start": {"line": 131, "column": 23}, "end": {"line": 131, "column": 30}}, "loc": {"start": {"line": 131, "column": 34}, "end": {"line": 131, "column": 57}}}, "9": {"name": "(anonymous_11)", "decl": {"start": {"line": 134, "column": 10}, "end": {"line": 134, "column": 15}}, "loc": {"start": {"line": 134, "column": 75}, "end": {"line": 138, "column": 3}}}, "10": {"name": "(anonymous_12)", "decl": {"start": {"line": 140, "column": 10}, "end": {"line": 140, "column": 15}}, "loc": {"start": {"line": 140, "column": 55}, "end": {"line": 143, "column": 3}}}, "11": {"name": "(anonymous_14)", "decl": {"start": {"line": 167, "column": 10}, "end": {"line": 167, "column": 15}}, "loc": {"start": {"line": 167, "column": 50}, "end": {"line": 170, "column": 3}}}, "12": {"name": "(anonymous_15)", "decl": {"start": {"line": 172, "column": 10}, "end": {"line": 172, "column": 15}}, "loc": {"start": {"line": 175, "column": 22}, "end": {"line": 178, "column": 3}}}, "13": {"name": "(anonymous_16)", "decl": {"start": {"line": 183, "column": 2}, "end": {"line": 183, "column": 7}}, "loc": {"start": {"line": 187, "column": 6}, "end": {"line": 219, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 22, "column": 4}, "end": {"line": 22, "column": 30}}, "type": "default-arg", "locations": [{"start": {"line": 22, "column": 28}, "end": {"line": 22, "column": 30}}]}, "1": {"loc": {"start": {"line": 25, "column": 6}, "end": {"line": 25, "column": 20}}, "type": "default-arg", "locations": [{"start": {"line": 25, "column": 19}, "end": {"line": 25, "column": 20}}]}, "2": {"loc": {"start": {"line": 26, "column": 6}, "end": {"line": 26, "column": 21}}, "type": "default-arg", "locations": [{"start": {"line": 26, "column": 18}, "end": {"line": 26, "column": 21}}]}, "3": {"loc": {"start": {"line": 27, "column": 6}, "end": {"line": 27, "column": 21}}, "type": "default-arg", "locations": [{"start": {"line": 27, "column": 17}, "end": {"line": 27, "column": 21}}]}, "4": {"loc": {"start": {"line": 28, "column": 6}, "end": {"line": 28, "column": 23}}, "type": "default-arg", "locations": [{"start": {"line": 28, "column": 22}, "end": {"line": 28, "column": 23}}]}, "5": {"loc": {"start": {"line": 42, "column": 8}, "end": {"line": 59, "column": 9}}, "type": "if", "locations": [{"start": {"line": 42, "column": 8}, "end": {"line": 59, "column": 9}}]}, "6": {"loc": {"start": {"line": 42, "column": 12}, "end": {"line": 44, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 42, "column": 12}, "end": {"line": 42, "column": 63}}, {"start": {"line": 43, "column": 12}, "end": {"line": 43, "column": 46}}, {"start": {"line": 44, "column": 12}, "end": {"line": 44, "column": 49}}]}, "7": {"loc": {"start": {"line": 46, "column": 10}, "end": {"line": 58, "column": 11}}, "type": "if", "locations": [{"start": {"line": 46, "column": 10}, "end": {"line": 58, "column": 11}}]}, "8": {"loc": {"start": {"line": 88, "column": 4}, "end": {"line": 88, "column": 27}}, "type": "default-arg", "locations": [{"start": {"line": 88, "column": 25}, "end": {"line": 88, "column": 27}}]}, "9": {"loc": {"start": {"line": 96, "column": 6}, "end": {"line": 98, "column": 7}}, "type": "if", "locations": [{"start": {"line": 96, "column": 6}, "end": {"line": 98, "column": 7}}]}, "10": {"loc": {"start": {"line": 111, "column": 4}, "end": {"line": 115, "column": 10}}, "type": "default-arg", "locations": [{"start": {"line": 115, "column": 8}, "end": {"line": 115, "column": 10}}]}, "11": {"loc": {"start": {"line": 120, "column": 6}, "end": {"line": 120, "column": 26}}, "type": "default-arg", "locations": [{"start": {"line": 120, "column": 25}, "end": {"line": 120, "column": 26}}]}, "12": {"loc": {"start": {"line": 121, "column": 6}, "end": {"line": 121, "column": 26}}, "type": "default-arg", "locations": [{"start": {"line": 121, "column": 21}, "end": {"line": 121, "column": 26}}]}, "13": {"loc": {"start": {"line": 122, "column": 6}, "end": {"line": 122, "column": 30}}, "type": "default-arg", "locations": [{"start": {"line": 122, "column": 25}, "end": {"line": 122, "column": 30}}]}, "14": {"loc": {"start": {"line": 151, "column": 4}, "end": {"line": 151, "column": 29}}, "type": "default-arg", "locations": [{"start": {"line": 151, "column": 25}, "end": {"line": 151, "column": 29}}]}, "15": {"loc": {"start": {"line": 155, "column": 4}, "end": {"line": 158, "column": 5}}, "type": "if", "locations": [{"start": {"line": 155, "column": 4}, "end": {"line": 158, "column": 5}}]}, "16": {"loc": {"start": {"line": 199, "column": 8}, "end": {"line": 201, "column": 9}}, "type": "if", "locations": [{"start": {"line": 199, "column": 8}, "end": {"line": 201, "column": 9}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0}, "b": {"0": [0], "1": [0], "2": [0], "3": [0], "4": [0], "5": [0], "6": [0, 0, 0], "7": [0], "8": [0], "9": [0], "10": [0], "11": [0], "12": [0], "13": [0], "14": [0], "15": [0], "16": [0]}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/common/services/redis.service.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/common/services/redis.service.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 83}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 47}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 54}}, "3": {"start": {"line": 6, "column": 25}, "end": {"line": 130, "column": null}}, "4": {"start": {"line": 7, "column": 28}, "end": {"line": 7, "column": 57}}, "5": {"start": {"line": 10, "column": 31}, "end": {"line": 10, "column": 46}}, "6": {"start": {"line": 13, "column": 24}, "end": {"line": 13, "column": 55}}, "7": {"start": {"line": 15, "column": 4}, "end": {"line": 22, "column": 7}}, "8": {"start": {"line": 24, "column": 4}, "end": {"line": 26, "column": 7}}, "9": {"start": {"line": 25, "column": 6}, "end": {"line": 25, "column": 51}}, "10": {"start": {"line": 28, "column": 4}, "end": {"line": 30, "column": 7}}, "11": {"start": {"line": 29, "column": 6}, "end": {"line": 29, "column": 44}}, "12": {"start": {"line": 32, "column": 4}, "end": {"line": 34, "column": 7}}, "13": {"start": {"line": 33, "column": 6}, "end": {"line": 33, "column": 50}}, "14": {"start": {"line": 36, "column": 4}, "end": {"line": 40, "column": 5}}, "15": {"start": {"line": 37, "column": 6}, "end": {"line": 37, "column": 34}}, "16": {"start": {"line": 39, "column": 6}, "end": {"line": 39, "column": 61}}, "17": {"start": {"line": 44, "column": 4}, "end": {"line": 46, "column": 5}}, "18": {"start": {"line": 45, "column": 6}, "end": {"line": 45, "column": 37}}, "19": {"start": {"line": 50, "column": 4}, "end": {"line": 55, "column": 5}}, "20": {"start": {"line": 51, "column": 6}, "end": {"line": 51, "column": 40}}, "21": {"start": {"line": 53, "column": 6}, "end": {"line": 53, "column": 59}}, "22": {"start": {"line": 54, "column": 6}, "end": {"line": 54, "column": 18}}, "23": {"start": {"line": 59, "column": 4}, "end": {"line": 69, "column": 5}}, "24": {"start": {"line": 60, "column": 6}, "end": {"line": 64, "column": 7}}, "25": {"start": {"line": 61, "column": 8}, "end": {"line": 61, "column": 56}}, "26": {"start": {"line": 63, "column": 8}, "end": {"line": 63, "column": 42}}, "27": {"start": {"line": 65, "column": 6}, "end": {"line": 65, "column": 18}}, "28": {"start": {"line": 67, "column": 6}, "end": {"line": 67, "column": 59}}, "29": {"start": {"line": 68, "column": 6}, "end": {"line": 68, "column": 19}}, "30": {"start": {"line": 73, "column": 4}, "end": {"line": 79, "column": 5}}, "31": {"start": {"line": 74, "column": 6}, "end": {"line": 74, "column": 33}}, "32": {"start": {"line": 75, "column": 6}, "end": {"line": 75, "column": 18}}, "33": {"start": {"line": 77, "column": 6}, "end": {"line": 77, "column": 60}}, "34": {"start": {"line": 78, "column": 6}, "end": {"line": 78, "column": 19}}, "35": {"start": {"line": 83, "column": 4}, "end": {"line": 89, "column": 5}}, "36": {"start": {"line": 84, "column": 21}, "end": {"line": 84, "column": 50}}, "37": {"start": {"line": 85, "column": 6}, "end": {"line": 85, "column": 26}}, "38": {"start": {"line": 87, "column": 6}, "end": {"line": 87, "column": 73}}, "39": {"start": {"line": 88, "column": 6}, "end": {"line": 88, "column": 19}}, "40": {"start": {"line": 93, "column": 4}, "end": {"line": 98, "column": 5}}, "41": {"start": {"line": 94, "column": 6}, "end": {"line": 94, "column": 41}}, "42": {"start": {"line": 96, "column": 6}, "end": {"line": 96, "column": 64}}, "43": {"start": {"line": 97, "column": 6}, "end": {"line": 97, "column": 15}}, "44": {"start": {"line": 102, "column": 4}, "end": {"line": 108, "column": 5}}, "45": {"start": {"line": 103, "column": 6}, "end": {"line": 103, "column": 45}}, "46": {"start": {"line": 104, "column": 6}, "end": {"line": 104, "column": 18}}, "47": {"start": {"line": 106, "column": 6}, "end": {"line": 106, "column": 74}}, "48": {"start": {"line": 107, "column": 6}, "end": {"line": 107, "column": 19}}, "49": {"start": {"line": 112, "column": 4}, "end": {"line": 118, "column": 5}}, "50": {"start": {"line": 113, "column": 20}, "end": {"line": 113, "column": 39}}, "51": {"start": {"line": 114, "column": 6}, "end": {"line": 114, "column": 46}}, "52": {"start": {"line": 116, "column": 6}, "end": {"line": 116, "column": 68}}, "53": {"start": {"line": 117, "column": 6}, "end": {"line": 117, "column": 18}}, "54": {"start": {"line": 122, "column": 4}, "end": {"line": 128, "column": 5}}, "55": {"start": {"line": 123, "column": 24}, "end": {"line": 123, "column": 45}}, "56": {"start": {"line": 124, "column": 6}, "end": {"line": 124, "column": 56}}, "57": {"start": {"line": 126, "column": 6}, "end": {"line": 126, "column": 68}}, "58": {"start": {"line": 127, "column": 6}, "end": {"line": 127, "column": 19}}, "59": {"start": {"line": 6, "column": 13}, "end": {"line": 6, "column": 25}}, "60": {"start": {"line": 6, "column": 13}, "end": {"line": 130, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 10, "column": 2}, "end": {"line": 10, "column": 31}}, "loc": {"start": {"line": 10, "column": 59}, "end": {"line": 10, "column": 63}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": 7}}, "loc": {"start": {"line": 12, "column": 20}, "end": {"line": 41, "column": 3}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 24, "column": 28}, "end": {"line": 24, "column": 29}}, "loc": {"start": {"line": 24, "column": 36}, "end": {"line": 26, "column": 5}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 28, "column": 30}, "end": {"line": 28, "column": 33}}, "loc": {"start": {"line": 28, "column": 35}, "end": {"line": 30, "column": 5}}}, "4": {"name": "(anonymous_6)", "decl": {"start": {"line": 32, "column": 33}, "end": {"line": 32, "column": 36}}, "loc": {"start": {"line": 32, "column": 38}, "end": {"line": 34, "column": 5}}}, "5": {"name": "(anonymous_7)", "decl": {"start": {"line": 43, "column": 2}, "end": {"line": 43, "column": 7}}, "loc": {"start": {"line": 43, "column": 23}, "end": {"line": 47, "column": 3}}}, "6": {"name": "(anonymous_8)", "decl": {"start": {"line": 49, "column": 2}, "end": {"line": 49, "column": 7}}, "loc": {"start": {"line": 49, "column": 23}, "end": {"line": 56, "column": 3}}}, "7": {"name": "(anonymous_9)", "decl": {"start": {"line": 58, "column": 2}, "end": {"line": 58, "column": 7}}, "loc": {"start": {"line": 58, "column": 59}, "end": {"line": 70, "column": 3}}}, "8": {"name": "(anonymous_10)", "decl": {"start": {"line": 72, "column": 2}, "end": {"line": 72, "column": 7}}, "loc": {"start": {"line": 72, "column": 23}, "end": {"line": 80, "column": 3}}}, "9": {"name": "(anonymous_11)", "decl": {"start": {"line": 82, "column": 2}, "end": {"line": 82, "column": 7}}, "loc": {"start": {"line": 82, "column": 26}, "end": {"line": 90, "column": 3}}}, "10": {"name": "(anonymous_12)", "decl": {"start": {"line": 92, "column": 2}, "end": {"line": 92, "column": 7}}, "loc": {"start": {"line": 92, "column": 24}, "end": {"line": 99, "column": 3}}}, "11": {"name": "(anonymous_13)", "decl": {"start": {"line": 101, "column": 2}, "end": {"line": 101, "column": 7}}, "loc": {"start": {"line": 101, "column": 43}, "end": {"line": 109, "column": 3}}}, "12": {"name": "(anonymous_14)", "decl": {"start": {"line": 111, "column": 2}, "end": {"line": 111, "column": 7}}, "loc": {"start": {"line": 111, "column": 30}, "end": {"line": 119, "column": 3}}}, "13": {"name": "(anonymous_15)", "decl": {"start": {"line": 121, "column": 2}, "end": {"line": 121, "column": 7}}, "loc": {"start": {"line": 121, "column": 61}, "end": {"line": 129, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 44, "column": 4}, "end": {"line": 46, "column": 5}}, "type": "if", "locations": [{"start": {"line": 44, "column": 4}, "end": {"line": 46, "column": 5}}]}, "1": {"loc": {"start": {"line": 60, "column": 6}, "end": {"line": 64, "column": 7}}, "type": "if", "locations": [{"start": {"line": 60, "column": 6}, "end": {"line": 64, "column": 7}}, {"start": {"line": 62, "column": 13}, "end": {"line": 64, "column": 7}}]}, "2": {"loc": {"start": {"line": 114, "column": 13}, "end": {"line": 114, "column": 45}}, "type": "cond-expr", "locations": [{"start": {"line": 114, "column": 21}, "end": {"line": 114, "column": 38}}, {"start": {"line": 114, "column": 41}, "end": {"line": 114, "column": 45}}]}}, "s": {"0": 2, "1": 2, "2": 2, "3": 2, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 2, "60": 2}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0}, "b": {"0": [0], "1": [0, 0], "2": [0, 0]}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/config/app.config.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/config/app.config.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 44}}, "1": {"start": {"line": 3, "column": 0}, "end": {"line": 29, "column": 4}}, "2": {"start": {"line": 3, "column": 40}, "end": {"line": 29, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 3, "column": 33}, "end": {"line": 3, "column": 36}}, "loc": {"start": {"line": 3, "column": 40}, "end": {"line": 29, "column": 2}}}}, "branchMap": {"0": {"loc": {"start": {"line": 4, "column": 11}, "end": {"line": 4, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 4, "column": 11}, "end": {"line": 4, "column": 31}}, {"start": {"line": 4, "column": 35}, "end": {"line": 4, "column": 48}}]}, "1": {"loc": {"start": {"line": 5, "column": 17}, "end": {"line": 5, "column": 43}}, "type": "binary-expr", "locations": [{"start": {"line": 5, "column": 17}, "end": {"line": 5, "column": 33}}, {"start": {"line": 5, "column": 37}, "end": {"line": 5, "column": 43}}]}, "2": {"loc": {"start": {"line": 6, "column": 8}, "end": {"line": 6, "column": 56}}, "type": "binary-expr", "locations": [{"start": {"line": 6, "column": 8}, "end": {"line": 6, "column": 28}}, {"start": {"line": 6, "column": 32}, "end": {"line": 6, "column": 56}}]}, "3": {"loc": {"start": {"line": 9, "column": 12}, "end": {"line": 9, "column": 58}}, "type": "binary-expr", "locations": [{"start": {"line": 9, "column": 12}, "end": {"line": 9, "column": 34}}, {"start": {"line": 9, "column": 38}, "end": {"line": 9, "column": 58}}]}, "4": {"loc": {"start": {"line": 10, "column": 15}, "end": {"line": 10, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 10, "column": 15}, "end": {"line": 10, "column": 41}}, {"start": {"line": 10, "column": 45}, "end": {"line": 10, "column": 50}}]}, "5": {"loc": {"start": {"line": 15, "column": 13}, "end": {"line": 15, "column": 90}}, "type": "binary-expr", "locations": [{"start": {"line": 15, "column": 13}, "end": {"line": 15, "column": 45}}, {"start": {"line": 15, "column": 49}, "end": {"line": 15, "column": 90}}]}, "6": {"loc": {"start": {"line": 19, "column": 18}, "end": {"line": 19, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 19, "column": 18}, "end": {"line": 19, "column": 42}}, {"start": {"line": 19, "column": 46}, "end": {"line": 19, "column": 50}}]}, "7": {"loc": {"start": {"line": 20, "column": 20}, "end": {"line": 20, "column": 54}}, "type": "binary-expr", "locations": [{"start": {"line": 20, "column": 20}, "end": {"line": 20, "column": 46}}, {"start": {"line": 20, "column": 50}, "end": {"line": 20, "column": 54}}]}, "8": {"loc": {"start": {"line": 24, "column": 11}, "end": {"line": 24, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 24, "column": 11}, "end": {"line": 24, "column": 36}}, {"start": {"line": 24, "column": 40}, "end": {"line": 24, "column": 64}}]}, "9": {"loc": {"start": {"line": 25, "column": 17}, "end": {"line": 25, "column": 99}}, "type": "binary-expr", "locations": [{"start": {"line": 25, "column": 17}, "end": {"line": 25, "column": 48}}, {"start": {"line": 25, "column": 52}, "end": {"line": 25, "column": 99}}]}, "10": {"loc": {"start": {"line": 26, "column": 13}, "end": {"line": 26, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 26, "column": 13}, "end": {"line": 26, "column": 40}}, {"start": {"line": 26, "column": 44}, "end": {"line": 26, "column": 49}}]}, "11": {"loc": {"start": {"line": 27, "column": 10}, "end": {"line": 27, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 27, "column": 10}, "end": {"line": 27, "column": 34}}, {"start": {"line": 27, "column": 38}, "end": {"line": 27, "column": 48}}]}}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0], "6": [0, 0], "7": [0, 0], "8": [0, 0], "9": [0, 0], "10": [0, 0], "11": [0, 0]}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/config/database.config.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/config/database.config.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 44}}, "1": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 63}}, "2": {"start": {"line": 5, "column": 0}, "end": {"line": 21, "column": 2}}, "3": {"start": {"line": 7, "column": 31}, "end": {"line": 20, "column": 4}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 7, "column": 2}, "end": {"line": 7, "column": 27}}, "loc": {"start": {"line": 7, "column": 31}, "end": {"line": 20, "column": 4}}}}, "branchMap": {"0": {"loc": {"start": {"line": 9, "column": 10}, "end": {"line": 9, "column": 44}}, "type": "binary-expr", "locations": [{"start": {"line": 9, "column": 10}, "end": {"line": 9, "column": 29}}, {"start": {"line": 9, "column": 33}, "end": {"line": 9, "column": 44}}]}, "1": {"loc": {"start": {"line": 10, "column": 19}, "end": {"line": 10, "column": 48}}, "type": "binary-expr", "locations": [{"start": {"line": 10, "column": 19}, "end": {"line": 10, "column": 38}}, {"start": {"line": 10, "column": 42}, "end": {"line": 10, "column": 48}}]}, "2": {"loc": {"start": {"line": 11, "column": 14}, "end": {"line": 11, "column": 51}}, "type": "binary-expr", "locations": [{"start": {"line": 11, "column": 14}, "end": {"line": 11, "column": 37}}, {"start": {"line": 11, "column": 41}, "end": {"line": 11, "column": 51}}]}, "3": {"loc": {"start": {"line": 12, "column": 14}, "end": {"line": 12, "column": 51}}, "type": "binary-expr", "locations": [{"start": {"line": 12, "column": 14}, "end": {"line": 12, "column": 37}}, {"start": {"line": 12, "column": 41}, "end": {"line": 12, "column": 51}}]}, "4": {"loc": {"start": {"line": 13, "column": 14}, "end": {"line": 13, "column": 57}}, "type": "binary-expr", "locations": [{"start": {"line": 13, "column": 14}, "end": {"line": 13, "column": 33}}, {"start": {"line": 13, "column": 37}, "end": {"line": 13, "column": 57}}]}, "5": {"loc": {"start": {"line": 18, "column": 9}, "end": {"line": 18, "column": 86}}, "type": "cond-expr", "locations": [{"start": {"line": 18, "column": 49}, "end": {"line": 18, "column": 78}}, {"start": {"line": 18, "column": 81}, "end": {"line": 18, "column": 86}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0}, "f": {"0": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0], "5": [0, 0]}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/config/index.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/config/index.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 9}}, "1": {"start": {"line": 1, "column": 9}, "end": {"line": 1, "column": 29}}, "2": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 9}}, "3": {"start": {"line": 2, "column": 9}, "end": {"line": 2, "column": 34}}, "4": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 9}}, "5": {"start": {"line": 3, "column": 9}, "end": {"line": 3, "column": 31}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 1, "column": 9}, "end": {"line": 1, "column": 16}}, "loc": {"start": {"line": 1, "column": 9}, "end": {"line": 1, "column": 29}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 2, "column": 9}, "end": {"line": 2, "column": 16}}, "loc": {"start": {"line": 2, "column": 9}, "end": {"line": 2, "column": 34}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 3, "column": 9}, "end": {"line": 3, "column": 16}}, "loc": {"start": {"line": 3, "column": 9}, "end": {"line": 3, "column": 31}}}}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "f": {"0": 0, "1": 0, "2": 0}, "b": {}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/config/redis.config.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/config/redis.config.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 44}}, "1": {"start": {"line": 3, "column": 0}, "end": {"line": 8, "column": 4}}, "2": {"start": {"line": 3, "column": 42}, "end": {"line": 8, "column": 2}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 3, "column": 35}, "end": {"line": 3, "column": 38}}, "loc": {"start": {"line": 3, "column": 42}, "end": {"line": 8, "column": 2}}}}, "branchMap": {"0": {"loc": {"start": {"line": 4, "column": 8}, "end": {"line": 4, "column": 45}}, "type": "binary-expr", "locations": [{"start": {"line": 4, "column": 8}, "end": {"line": 4, "column": 30}}, {"start": {"line": 4, "column": 34}, "end": {"line": 4, "column": 45}}]}, "1": {"loc": {"start": {"line": 5, "column": 17}, "end": {"line": 5, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 5, "column": 17}, "end": {"line": 5, "column": 39}}, {"start": {"line": 5, "column": 43}, "end": {"line": 5, "column": 49}}]}, "2": {"loc": {"start": {"line": 6, "column": 12}, "end": {"line": 6, "column": 51}}, "type": "binary-expr", "locations": [{"start": {"line": 6, "column": 12}, "end": {"line": 6, "column": 38}}, {"start": {"line": 6, "column": 42}, "end": {"line": 6, "column": 51}}]}}, "s": {"0": 0, "1": 0, "2": 0}, "f": {"0": 0}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0]}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/config/typeorm.config.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/config/typeorm.config.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 37}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 32}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 63}}, "3": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 9}}, "4": {"start": {"line": 7, "column": 0}, "end": {"line": 18, "column": 3}}}, "fnMap": {}, "branchMap": {"0": {"loc": {"start": {"line": 9, "column": 8}, "end": {"line": 9, "column": 42}}, "type": "binary-expr", "locations": [{"start": {"line": 9, "column": 8}, "end": {"line": 9, "column": 27}}, {"start": {"line": 9, "column": 31}, "end": {"line": 9, "column": 42}}]}, "1": {"loc": {"start": {"line": 10, "column": 17}, "end": {"line": 10, "column": 46}}, "type": "binary-expr", "locations": [{"start": {"line": 10, "column": 17}, "end": {"line": 10, "column": 36}}, {"start": {"line": 10, "column": 40}, "end": {"line": 10, "column": 46}}]}, "2": {"loc": {"start": {"line": 11, "column": 12}, "end": {"line": 11, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 11, "column": 12}, "end": {"line": 11, "column": 35}}, {"start": {"line": 11, "column": 39}, "end": {"line": 11, "column": 49}}]}, "3": {"loc": {"start": {"line": 12, "column": 12}, "end": {"line": 12, "column": 49}}, "type": "binary-expr", "locations": [{"start": {"line": 12, "column": 12}, "end": {"line": 12, "column": 35}}, {"start": {"line": 12, "column": 39}, "end": {"line": 12, "column": 49}}]}, "4": {"loc": {"start": {"line": 13, "column": 12}, "end": {"line": 13, "column": 55}}, "type": "binary-expr", "locations": [{"start": {"line": 13, "column": 12}, "end": {"line": 13, "column": 31}}, {"start": {"line": 13, "column": 35}, "end": {"line": 13, "column": 55}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0}, "f": {}, "b": {"0": [0, 0], "1": [0, 0], "2": [0, 0], "3": [0, 0], "4": [0, 0]}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/dto/auth.dto.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/dto/auth.dto.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 63}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 46}}, "2": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 13}}, "3": {"start": {"line": 7, "column": 2}, "end": {"line": 7, "column": null}}, "4": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": null}}, "5": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 13}}, "6": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": null}}, "7": {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": null}}, "8": {"start": {"line": 27, "column": 2}, "end": {"line": 27, "column": null}}, "9": {"start": {"line": 31, "column": 2}, "end": {"line": 31, "column": null}}, "10": {"start": {"line": 34, "column": 0}, "end": {"line": 34, "column": 13}}, "11": {"start": {"line": 36, "column": 2}, "end": {"line": 36, "column": null}}, "12": {"start": {"line": 39, "column": 2}, "end": {"line": 45, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 5, "1": 5, "2": 5, "3": 5, "4": 5, "5": 5, "6": 5, "7": 5, "8": 5, "9": 5, "10": 5, "11": 5, "12": 5}, "f": {}, "b": {}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/dto/driver.dto.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/dto/driver.dto.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 66}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 67}}, "2": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 13}}, "3": {"start": {"line": 8, "column": 2}, "end": {"line": 8, "column": null}}, "4": {"start": {"line": 13, "column": 2}, "end": {"line": 13, "column": null}}, "5": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": null}}, "6": {"start": {"line": 21, "column": 0}, "end": {"line": 21, "column": 13}}, "7": {"start": {"line": 26, "column": 2}, "end": {"line": 26, "column": null}}, "8": {"start": {"line": 32, "column": 2}, "end": {"line": 32, "column": null}}, "9": {"start": {"line": 38, "column": 2}, "end": {"line": 38, "column": null}}, "10": {"start": {"line": 41, "column": 0}, "end": {"line": 41, "column": 13}}, "11": {"start": {"line": 43, "column": 2}, "end": {"line": 43, "column": null}}, "12": {"start": {"line": 46, "column": 2}, "end": {"line": 46, "column": null}}, "13": {"start": {"line": 49, "column": 2}, "end": {"line": 49, "column": null}}, "14": {"start": {"line": 52, "column": 2}, "end": {"line": 52, "column": null}}, "15": {"start": {"line": 55, "column": 2}, "end": {"line": 55, "column": null}}, "16": {"start": {"line": 58, "column": 2}, "end": {"line": 58, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 5, "1": 5, "2": 5, "3": 5, "4": 5, "5": 5, "6": 5, "7": 5, "8": 5, "9": 5, "10": 5, "11": 5, "12": 5, "13": 5, "14": 5, "15": 5, "16": 5}, "f": {}, "b": {}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/dto/index.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/dto/index.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 27}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 27}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 29}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 27}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 29}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 30}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 5, "1": 5, "2": 5, "3": 5, "4": 5, "5": 5}, "f": {}, "b": {}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/dto/ticket.dto.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/dto/ticket.dto.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 61}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 67}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 43}}, "3": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 13}}, "4": {"start": {"line": 8, "column": 2}, "end": {"line": 8, "column": null}}, "5": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": null}}, "6": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 13}}, "7": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": null}}, "8": {"start": {"line": 22, "column": 0}, "end": {"line": 22, "column": 13}}, "9": {"start": {"line": 24, "column": 2}, "end": {"line": 24, "column": null}}, "10": {"start": {"line": 27, "column": 2}, "end": {"line": 27, "column": null}}, "11": {"start": {"line": 30, "column": 2}, "end": {"line": 30, "column": null}}, "12": {"start": {"line": 33, "column": 2}, "end": {"line": 33, "column": null}}, "13": {"start": {"line": 36, "column": 2}, "end": {"line": 36, "column": null}}, "14": {"start": {"line": 39, "column": 2}, "end": {"line": 39, "column": null}}, "15": {"start": {"line": 42, "column": 2}, "end": {"line": 42, "column": null}}, "16": {"start": {"line": 45, "column": 2}, "end": {"line": 45, "column": null}}, "17": {"start": {"line": 48, "column": 2}, "end": {"line": 55, "column": null}}, "18": {"start": {"line": 58, "column": 2}, "end": {"line": 61, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 5, "1": 5, "2": 5, "3": 5, "4": 5, "5": 5, "6": 5, "7": 5, "8": 5, "9": 5, "10": 5, "11": 5, "12": 5, "13": 5, "14": 5, "15": 5, "16": 5, "17": 5, "18": 5}, "f": {}, "b": {}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/dto/trip.dto.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/dto/trip.dto.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 97}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 67}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 41}}, "3": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 13}}, "4": {"start": {"line": 8, "column": 2}, "end": {"line": 8, "column": null}}, "5": {"start": {"line": 12, "column": 2}, "end": {"line": 12, "column": null}}, "6": {"start": {"line": 16, "column": 2}, "end": {"line": 16, "column": null}}, "7": {"start": {"line": 20, "column": 2}, "end": {"line": 20, "column": null}}, "8": {"start": {"line": 25, "column": 2}, "end": {"line": 25, "column": null}}, "9": {"start": {"line": 31, "column": 2}, "end": {"line": 31, "column": null}}, "10": {"start": {"line": 35, "column": 2}, "end": {"line": 35, "column": null}}, "11": {"start": {"line": 38, "column": 0}, "end": {"line": 38, "column": 13}}, "12": {"start": {"line": 42, "column": 2}, "end": {"line": 42, "column": null}}, "13": {"start": {"line": 47, "column": 2}, "end": {"line": 47, "column": null}}, "14": {"start": {"line": 52, "column": 2}, "end": {"line": 52, "column": null}}, "15": {"start": {"line": 57, "column": 2}, "end": {"line": 57, "column": null}}, "16": {"start": {"line": 63, "column": 2}, "end": {"line": 63, "column": null}}, "17": {"start": {"line": 68, "column": 2}, "end": {"line": 68, "column": null}}, "18": {"start": {"line": 89, "column": 18}, "end": {"line": 89, "column": 19}}, "19": {"start": {"line": 97, "column": 19}, "end": {"line": 97, "column": 21}}, "20": {"start": {"line": 71, "column": 0}, "end": {"line": 71, "column": 13}}, "21": {"start": {"line": 74, "column": 2}, "end": {"line": 74, "column": null}}, "22": {"start": {"line": 78, "column": 2}, "end": {"line": 78, "column": null}}, "23": {"start": {"line": 82, "column": 2}, "end": {"line": 82, "column": null}}, "24": {"start": {"line": 89, "column": 2}, "end": {"line": 89, "column": null}}, "25": {"start": {"line": 86, "column": 14}, "end": {"line": 86, "column": 20}}, "26": {"start": {"line": 97, "column": 2}, "end": {"line": 97, "column": null}}, "27": {"start": {"line": 93, "column": 14}, "end": {"line": 93, "column": 20}}, "28": {"start": {"line": 100, "column": 0}, "end": {"line": 100, "column": 13}}, "29": {"start": {"line": 102, "column": 2}, "end": {"line": 102, "column": null}}, "30": {"start": {"line": 105, "column": 2}, "end": {"line": 105, "column": null}}, "31": {"start": {"line": 108, "column": 2}, "end": {"line": 108, "column": null}}, "32": {"start": {"line": 111, "column": 2}, "end": {"line": 111, "column": null}}, "33": {"start": {"line": 114, "column": 2}, "end": {"line": 114, "column": null}}, "34": {"start": {"line": 117, "column": 2}, "end": {"line": 117, "column": null}}, "35": {"start": {"line": 120, "column": 2}, "end": {"line": 120, "column": null}}, "36": {"start": {"line": 123, "column": 2}, "end": {"line": 123, "column": null}}, "37": {"start": {"line": 126, "column": 2}, "end": {"line": 126, "column": null}}, "38": {"start": {"line": 129, "column": 2}, "end": {"line": 129, "column": null}}, "39": {"start": {"line": 132, "column": 2}, "end": {"line": 132, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 86, "column": 8}, "end": {"line": 86, "column": 11}}, "loc": {"start": {"line": 86, "column": 14}, "end": {"line": 86, "column": 20}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 93, "column": 8}, "end": {"line": 93, "column": 11}}, "loc": {"start": {"line": 93, "column": 14}, "end": {"line": 93, "column": 20}}}}, "branchMap": {}, "s": {"0": 5, "1": 5, "2": 5, "3": 5, "4": 5, "5": 5, "6": 5, "7": 5, "8": 5, "9": 5, "10": 5, "11": 5, "12": 5, "13": 5, "14": 5, "15": 5, "16": 5, "17": 5, "18": 0, "19": 0, "20": 5, "21": 5, "22": 5, "23": 5, "24": 5, "25": 0, "26": 5, "27": 0, "28": 5, "29": 5, "30": 5, "31": 5, "32": 5, "33": 5, "34": 5, "35": 5, "36": 5, "37": 5, "38": 5, "39": 5}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/dto/user.dto.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/dto/user.dto.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 94}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 67}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 39}}, "3": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 13}}, "4": {"start": {"line": 8, "column": 2}, "end": {"line": 8, "column": null}}, "5": {"start": {"line": 13, "column": 2}, "end": {"line": 13, "column": null}}, "6": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": null}}, "7": {"start": {"line": 23, "column": 2}, "end": {"line": 23, "column": null}}, "8": {"start": {"line": 28, "column": 2}, "end": {"line": 28, "column": null}}, "9": {"start": {"line": 31, "column": 0}, "end": {"line": 31, "column": 13}}, "10": {"start": {"line": 35, "column": 2}, "end": {"line": 35, "column": null}}, "11": {"start": {"line": 41, "column": 2}, "end": {"line": 41, "column": null}}, "12": {"start": {"line": 47, "column": 2}, "end": {"line": 47, "column": null}}, "13": {"start": {"line": 52, "column": 2}, "end": {"line": 52, "column": null}}, "14": {"start": {"line": 55, "column": 0}, "end": {"line": 55, "column": 13}}, "15": {"start": {"line": 57, "column": 2}, "end": {"line": 57, "column": null}}, "16": {"start": {"line": 60, "column": 2}, "end": {"line": 60, "column": null}}, "17": {"start": {"line": 63, "column": 2}, "end": {"line": 63, "column": null}}, "18": {"start": {"line": 66, "column": 2}, "end": {"line": 66, "column": null}}, "19": {"start": {"line": 69, "column": 2}, "end": {"line": 69, "column": null}}, "20": {"start": {"line": 72, "column": 2}, "end": {"line": 72, "column": null}}, "21": {"start": {"line": 75, "column": 2}, "end": {"line": 75, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 5, "1": 5, "2": 5, "3": 5, "4": 5, "5": 5, "6": 5, "7": 5, "8": 5, "9": 5, "10": 5, "11": 5, "12": 5, "13": 5, "14": 5, "15": 5, "16": 5, "17": 5, "18": 5, "19": 5, "20": 5, "21": 5}, "f": {}, "b": {}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/dto/weather.dto.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/dto/weather.dto.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 64}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 46}}, "2": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 13}}, "3": {"start": {"line": 14, "column": 2}, "end": {"line": 14, "column": null}}, "4": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 13}}, "5": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": null}}, "6": {"start": {"line": 22, "column": 2}, "end": {"line": 22, "column": null}}, "7": {"start": {"line": 25, "column": 2}, "end": {"line": 25, "column": null}}, "8": {"start": {"line": 28, "column": 2}, "end": {"line": 28, "column": null}}, "9": {"start": {"line": 31, "column": 2}, "end": {"line": 31, "column": null}}, "10": {"start": {"line": 34, "column": 2}, "end": {"line": 34, "column": null}}, "11": {"start": {"line": 37, "column": 2}, "end": {"line": 37, "column": null}}, "12": {"start": {"line": 40, "column": 2}, "end": {"line": 40, "column": null}}, "13": {"start": {"line": 43, "column": 2}, "end": {"line": 43, "column": null}}, "14": {"start": {"line": 46, "column": 2}, "end": {"line": 46, "column": null}}, "15": {"start": {"line": 49, "column": 2}, "end": {"line": 49, "column": null}}, "16": {"start": {"line": 52, "column": 2}, "end": {"line": 52, "column": null}}, "17": {"start": {"line": 55, "column": 2}, "end": {"line": 55, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 5, "1": 5, "2": 5, "3": 5, "4": 5, "5": 5, "6": 5, "7": 5, "8": 5, "9": 5, "10": 5, "11": 5, "12": 5, "13": 5, "14": 5, "15": 5, "16": 5, "17": 5}, "f": {}, "b": {}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/entities/driver.entity.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/entities/driver.entity.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": null}}, "1": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 37}}, "2": {"start": {"line": 14, "column": 7}, "end": {"line": 36, "column": null}}, "3": {"start": {"line": 14, "column": 13}, "end": {"line": 14, "column": 19}}, "4": {"start": {"line": 16, "column": 2}, "end": {"line": 16, "column": null}}, "5": {"start": {"line": 19, "column": 2}, "end": {"line": 19, "column": null}}, "6": {"start": {"line": 22, "column": 2}, "end": {"line": 22, "column": null}}, "7": {"start": {"line": 25, "column": 2}, "end": {"line": 25, "column": null}}, "8": {"start": {"line": 28, "column": 2}, "end": {"line": 28, "column": null}}, "9": {"start": {"line": 31, "column": 2}, "end": {"line": 31, "column": null}}, "10": {"start": {"line": 35, "column": 2}, "end": {"line": 35, "column": null}}, "11": {"start": {"line": 34, "column": 19}, "end": {"line": 34, "column": 23}}, "12": {"start": {"line": 34, "column": 35}, "end": {"line": 34, "column": 46}}, "13": {"start": {"line": 14, "column": 13}, "end": {"line": 36, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 34, "column": 13}, "end": {"line": 34, "column": 16}}, "loc": {"start": {"line": 34, "column": 19}, "end": {"line": 34, "column": 23}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 34, "column": 25}, "end": {"line": 34, "column": 26}}, "loc": {"start": {"line": 34, "column": 35}, "end": {"line": 34, "column": 46}}}}, "branchMap": {}, "s": {"0": 11, "1": 11, "2": 11, "3": 11, "4": 11, "5": 11, "6": 11, "7": 11, "8": 11, "9": 11, "10": 11, "11": 0, "12": 0, "13": 11}, "f": {"0": 0, "1": 0}, "b": {}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/entities/index.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/entities/index.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 9}}, "1": {"start": {"line": 1, "column": 9}, "end": {"line": 1, "column": 15}}, "2": {"start": {"line": 1, "column": 15}, "end": {"line": 1, "column": 47}}, "3": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 9}}, "4": {"start": {"line": 2, "column": 9}, "end": {"line": 2, "column": 41}}, "5": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 9}}, "6": {"start": {"line": 3, "column": 9}, "end": {"line": 3, "column": 37}}, "7": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 9}}, "8": {"start": {"line": 4, "column": 9}, "end": {"line": 4, "column": 37}}, "9": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 9}}, "10": {"start": {"line": 5, "column": 9}, "end": {"line": 5, "column": 17}}, "11": {"start": {"line": 5, "column": 17}, "end": {"line": 5, "column": 55}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 1, "column": 9}, "end": {"line": 1, "column": 13}}, "loc": {"start": {"line": 1, "column": 9}, "end": {"line": 1, "column": 15}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 1, "column": 15}, "end": {"line": 1, "column": 23}}, "loc": {"start": {"line": 1, "column": 15}, "end": {"line": 1, "column": 47}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 2, "column": 9}, "end": {"line": 2, "column": 15}}, "loc": {"start": {"line": 2, "column": 9}, "end": {"line": 2, "column": 41}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 3, "column": 9}, "end": {"line": 3, "column": 13}}, "loc": {"start": {"line": 3, "column": 9}, "end": {"line": 3, "column": 37}}}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 4, "column": 9}, "end": {"line": 4, "column": 13}}, "loc": {"start": {"line": 4, "column": 9}, "end": {"line": 4, "column": 37}}}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 5, "column": 9}, "end": {"line": 5, "column": 15}}, "loc": {"start": {"line": 5, "column": 9}, "end": {"line": 5, "column": 17}}}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 5, "column": 17}, "end": {"line": 5, "column": 29}}, "loc": {"start": {"line": 5, "column": 17}, "end": {"line": 5, "column": 55}}}}, "branchMap": {}, "s": {"0": 11, "1": 35, "2": 65, "3": 11, "4": 24, "5": 11, "6": 38, "7": 11, "8": 37, "9": 11, "10": 27, "11": 35}, "f": {"0": 24, "1": 54, "2": 13, "3": 27, "4": 26, "5": 16, "6": 24}, "b": {}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/entities/seat.entity.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/entities/seat.entity.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": null}}, "1": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 37}}, "2": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 41}}, "3": {"start": {"line": 20, "column": 7}, "end": {"line": 52, "column": null}}, "4": {"start": {"line": 20, "column": 13}, "end": {"line": 20, "column": 17}}, "5": {"start": {"line": 22, "column": 2}, "end": {"line": 22, "column": null}}, "6": {"start": {"line": 25, "column": 2}, "end": {"line": 25, "column": null}}, "7": {"start": {"line": 28, "column": 2}, "end": {"line": 28, "column": null}}, "8": {"start": {"line": 31, "column": 2}, "end": {"line": 31, "column": null}}, "9": {"start": {"line": 34, "column": 2}, "end": {"line": 34, "column": null}}, "10": {"start": {"line": 37, "column": 2}, "end": {"line": 37, "column": null}}, "11": {"start": {"line": 40, "column": 2}, "end": {"line": 40, "column": null}}, "12": {"start": {"line": 48, "column": 2}, "end": {"line": 48, "column": null}}, "13": {"start": {"line": 43, "column": 19}, "end": {"line": 43, "column": 23}}, "14": {"start": {"line": 43, "column": 35}, "end": {"line": 43, "column": 45}}, "15": {"start": {"line": 51, "column": 2}, "end": {"line": 51, "column": null}}, "16": {"start": {"line": 50, "column": 18}, "end": {"line": 50, "column": 24}}, "17": {"start": {"line": 50, "column": 38}, "end": {"line": 50, "column": 49}}, "18": {"start": {"line": 20, "column": 13}, "end": {"line": 52, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 43, "column": 13}, "end": {"line": 43, "column": 16}}, "loc": {"start": {"line": 43, "column": 19}, "end": {"line": 43, "column": 23}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 43, "column": 25}, "end": {"line": 43, "column": 26}}, "loc": {"start": {"line": 43, "column": 35}, "end": {"line": 43, "column": 45}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 50, "column": 12}, "end": {"line": 50, "column": 15}}, "loc": {"start": {"line": 50, "column": 18}, "end": {"line": 50, "column": 24}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 50, "column": 26}, "end": {"line": 50, "column": 27}}, "loc": {"start": {"line": 50, "column": 38}, "end": {"line": 50, "column": 49}}}}, "branchMap": {}, "s": {"0": 11, "1": 11, "2": 11, "3": 11, "4": 11, "5": 11, "6": 11, "7": 11, "8": 11, "9": 11, "10": 11, "11": 11, "12": 11, "13": 0, "14": 0, "15": 11, "16": 0, "17": 0, "18": 11}, "f": {"0": 0, "1": 0, "2": 0, "3": 0}, "b": {}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/entities/ticket.entity.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/entities/ticket.entity.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": null}}, "1": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 37}}, "2": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 37}}, "3": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 37}}, "4": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": null}}, "5": {"start": {"line": 17, "column": 2}, "end": {"line": 17, "column": null}}, "6": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": null}}, "7": {"start": {"line": 25, "column": 7}, "end": {"line": 84, "column": null}}, "8": {"start": {"line": 78, "column": 4}, "end": {"line": 78, "column": 47}}, "9": {"start": {"line": 82, "column": 4}, "end": {"line": 82, "column": 50}}, "10": {"start": {"line": 25, "column": 13}, "end": {"line": 25, "column": 19}}, "11": {"start": {"line": 27, "column": 2}, "end": {"line": 27, "column": null}}, "12": {"start": {"line": 30, "column": 2}, "end": {"line": 30, "column": null}}, "13": {"start": {"line": 33, "column": 2}, "end": {"line": 33, "column": null}}, "14": {"start": {"line": 36, "column": 2}, "end": {"line": 36, "column": null}}, "15": {"start": {"line": 39, "column": 2}, "end": {"line": 39, "column": null}}, "16": {"start": {"line": 38, "column": 46}, "end": {"line": 38, "column": 65}}, "17": {"start": {"line": 46, "column": 2}, "end": {"line": 46, "column": null}}, "18": {"start": {"line": 49, "column": 2}, "end": {"line": 49, "column": null}}, "19": {"start": {"line": 52, "column": 2}, "end": {"line": 52, "column": null}}, "20": {"start": {"line": 60, "column": 2}, "end": {"line": 60, "column": null}}, "21": {"start": {"line": 55, "column": 19}, "end": {"line": 55, "column": 23}}, "22": {"start": {"line": 55, "column": 35}, "end": {"line": 55, "column": 47}}, "23": {"start": {"line": 67, "column": 2}, "end": {"line": 67, "column": null}}, "24": {"start": {"line": 62, "column": 19}, "end": {"line": 62, "column": 23}}, "25": {"start": {"line": 62, "column": 35}, "end": {"line": 62, "column": 47}}, "26": {"start": {"line": 74, "column": 2}, "end": {"line": 74, "column": null}}, "27": {"start": {"line": 69, "column": 18}, "end": {"line": 69, "column": 22}}, "28": {"start": {"line": 69, "column": 34}, "end": {"line": 69, "column": 45}}, "29": {"start": {"line": 25, "column": 13}, "end": {"line": 84, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 12}}, "loc": {"start": {"line": 16, "column": 24}, "end": {"line": 19, "column": 1}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 77, "column": 2}, "end": {"line": 77, "column": 6}}, "loc": {"start": {"line": 77, "column": 14}, "end": {"line": 79, "column": 3}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 81, "column": 2}, "end": {"line": 81, "column": 6}}, "loc": {"start": {"line": 81, "column": 17}, "end": {"line": 83, "column": 3}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 38, "column": 40}, "end": {"line": 38, "column": 43}}, "loc": {"start": {"line": 38, "column": 46}, "end": {"line": 38, "column": 65}}}, "4": {"name": "(anonymous_6)", "decl": {"start": {"line": 55, "column": 13}, "end": {"line": 55, "column": 16}}, "loc": {"start": {"line": 55, "column": 19}, "end": {"line": 55, "column": 23}}}, "5": {"name": "(anonymous_7)", "decl": {"start": {"line": 55, "column": 25}, "end": {"line": 55, "column": 26}}, "loc": {"start": {"line": 55, "column": 35}, "end": {"line": 55, "column": 47}}}, "6": {"name": "(anonymous_8)", "decl": {"start": {"line": 62, "column": 13}, "end": {"line": 62, "column": 16}}, "loc": {"start": {"line": 62, "column": 19}, "end": {"line": 62, "column": 23}}}, "7": {"name": "(anonymous_9)", "decl": {"start": {"line": 62, "column": 25}, "end": {"line": 62, "column": 26}}, "loc": {"start": {"line": 62, "column": 35}, "end": {"line": 62, "column": 47}}}, "8": {"name": "(anonymous_10)", "decl": {"start": {"line": 69, "column": 12}, "end": {"line": 69, "column": 15}}, "loc": {"start": {"line": 69, "column": 18}, "end": {"line": 69, "column": 22}}}, "9": {"name": "(anonymous_11)", "decl": {"start": {"line": 69, "column": 24}, "end": {"line": 69, "column": 25}}, "loc": {"start": {"line": 69, "column": 34}, "end": {"line": 69, "column": 45}}}}, "branchMap": {"0": {"loc": {"start": {"line": 16, "column": 12}, "end": {"line": 16, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 16, "column": 12}, "end": {"line": 16, "column": 24}}, {"start": {"line": 16, "column": 24}, "end": {"line": 16, "column": null}}]}}, "s": {"0": 11, "1": 11, "2": 11, "3": 11, "4": 11, "5": 11, "6": 11, "7": 11, "8": 0, "9": 0, "10": 11, "11": 11, "12": 11, "13": 11, "14": 11, "15": 11, "16": 0, "17": 11, "18": 11, "19": 11, "20": 11, "21": 0, "22": 0, "23": 11, "24": 0, "25": 0, "26": 11, "27": 0, "28": 0, "29": 11}, "f": {"0": 11, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0}, "b": {"0": [11, 11]}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/entities/trip.entity.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/entities/trip.entity.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": null}}, "1": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 41}}, "2": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 37}}, "3": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 41}}, "4": {"start": {"line": 25, "column": 7}, "end": {"line": 90, "column": null}}, "5": {"start": {"line": 80, "column": 4}, "end": {"line": 80, "column": 69}}, "6": {"start": {"line": 84, "column": 4}, "end": {"line": 84, "column": 37}}, "7": {"start": {"line": 88, "column": 4}, "end": {"line": 88, "column": 77}}, "8": {"start": {"line": 25, "column": 13}, "end": {"line": 25, "column": 17}}, "9": {"start": {"line": 27, "column": 2}, "end": {"line": 27, "column": null}}, "10": {"start": {"line": 30, "column": 2}, "end": {"line": 30, "column": null}}, "11": {"start": {"line": 33, "column": 2}, "end": {"line": 33, "column": null}}, "12": {"start": {"line": 36, "column": 2}, "end": {"line": 36, "column": null}}, "13": {"start": {"line": 39, "column": 2}, "end": {"line": 39, "column": null}}, "14": {"start": {"line": 42, "column": 2}, "end": {"line": 42, "column": null}}, "15": {"start": {"line": 45, "column": 2}, "end": {"line": 45, "column": null}}, "16": {"start": {"line": 48, "column": 2}, "end": {"line": 48, "column": null}}, "17": {"start": {"line": 51, "column": 2}, "end": {"line": 51, "column": null}}, "18": {"start": {"line": 54, "column": 2}, "end": {"line": 54, "column": null}}, "19": {"start": {"line": 57, "column": 2}, "end": {"line": 57, "column": null}}, "20": {"start": {"line": 60, "column": 2}, "end": {"line": 60, "column": null}}, "21": {"start": {"line": 68, "column": 2}, "end": {"line": 68, "column": null}}, "22": {"start": {"line": 63, "column": 19}, "end": {"line": 63, "column": 25}}, "23": {"start": {"line": 63, "column": 39}, "end": {"line": 63, "column": 51}}, "24": {"start": {"line": 73, "column": 2}, "end": {"line": 73, "column": null}}, "25": {"start": {"line": 70, "column": 19}, "end": {"line": 70, "column": 23}}, "26": {"start": {"line": 70, "column": 35}, "end": {"line": 70, "column": 44}}, "27": {"start": {"line": 76, "column": 2}, "end": {"line": 76, "column": null}}, "28": {"start": {"line": 75, "column": 19}, "end": {"line": 75, "column": 25}}, "29": {"start": {"line": 75, "column": 39}, "end": {"line": 75, "column": 50}}, "30": {"start": {"line": 25, "column": 13}, "end": {"line": 90, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 79, "column": 2}, "end": {"line": 79, "column": 6}}, "loc": {"start": {"line": 79, "column": 14}, "end": {"line": 81, "column": 3}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 83, "column": 2}, "end": {"line": 83, "column": 6}}, "loc": {"start": {"line": 83, "column": 19}, "end": {"line": 85, "column": 3}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 87, "column": 2}, "end": {"line": 87, "column": 6}}, "loc": {"start": {"line": 87, "column": 19}, "end": {"line": 89, "column": 3}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 63, "column": 13}, "end": {"line": 63, "column": 16}}, "loc": {"start": {"line": 63, "column": 19}, "end": {"line": 63, "column": 25}}}, "4": {"name": "(anonymous_6)", "decl": {"start": {"line": 63, "column": 27}, "end": {"line": 63, "column": 28}}, "loc": {"start": {"line": 63, "column": 39}, "end": {"line": 63, "column": 51}}}, "5": {"name": "(anonymous_7)", "decl": {"start": {"line": 70, "column": 13}, "end": {"line": 70, "column": 16}}, "loc": {"start": {"line": 70, "column": 19}, "end": {"line": 70, "column": 23}}}, "6": {"name": "(anonymous_8)", "decl": {"start": {"line": 70, "column": 25}, "end": {"line": 70, "column": 26}}, "loc": {"start": {"line": 70, "column": 35}, "end": {"line": 70, "column": 44}}}, "7": {"name": "(anonymous_9)", "decl": {"start": {"line": 75, "column": 13}, "end": {"line": 75, "column": 16}}, "loc": {"start": {"line": 75, "column": 19}, "end": {"line": 75, "column": 25}}}, "8": {"name": "(anonymous_10)", "decl": {"start": {"line": 75, "column": 27}, "end": {"line": 75, "column": 28}}, "loc": {"start": {"line": 75, "column": 39}, "end": {"line": 75, "column": 50}}}}, "branchMap": {}, "s": {"0": 11, "1": 11, "2": 11, "3": 11, "4": 11, "5": 0, "6": 0, "7": 0, "8": 11, "9": 11, "10": 11, "11": 11, "12": 11, "13": 11, "14": 11, "15": 11, "16": 11, "17": 11, "18": 11, "19": 11, "20": 11, "21": 11, "22": 0, "23": 0, "24": 11, "25": 0, "26": 0, "27": 11, "28": 0, "29": 0, "30": 11}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0}, "b": {}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/entities/user.entity.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/entities/user.entity.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": null}}, "1": {"start": {"line": 10, "column": 0}, "end": {"line": 10, "column": 44}}, "2": {"start": {"line": 11, "column": 0}, "end": {"line": 11, "column": 41}}, "3": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": null}}, "4": {"start": {"line": 14, "column": 2}, "end": {"line": 14, "column": null}}, "5": {"start": {"line": 15, "column": 2}, "end": {"line": 15, "column": null}}, "6": {"start": {"line": 20, "column": 7}, "end": {"line": 58, "column": null}}, "7": {"start": {"line": 56, "column": 4}, "end": {"line": 56, "column": 48}}, "8": {"start": {"line": 20, "column": 13}, "end": {"line": 20, "column": 17}}, "9": {"start": {"line": 22, "column": 2}, "end": {"line": 22, "column": null}}, "10": {"start": {"line": 25, "column": 2}, "end": {"line": 25, "column": null}}, "11": {"start": {"line": 29, "column": 2}, "end": {"line": 29, "column": null}}, "12": {"start": {"line": 32, "column": 2}, "end": {"line": 32, "column": null}}, "13": {"start": {"line": 35, "column": 2}, "end": {"line": 35, "column": null}}, "14": {"start": {"line": 42, "column": 2}, "end": {"line": 42, "column": null}}, "15": {"start": {"line": 45, "column": 2}, "end": {"line": 45, "column": null}}, "16": {"start": {"line": 48, "column": 2}, "end": {"line": 48, "column": null}}, "17": {"start": {"line": 52, "column": 2}, "end": {"line": 52, "column": null}}, "18": {"start": {"line": 51, "column": 19}, "end": {"line": 51, "column": 25}}, "19": {"start": {"line": 51, "column": 39}, "end": {"line": 51, "column": 50}}, "20": {"start": {"line": 20, "column": 13}, "end": {"line": 58, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 12}}, "loc": {"start": {"line": 13, "column": 20}, "end": {"line": 16, "column": 1}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 55, "column": 2}, "end": {"line": 55, "column": 6}}, "loc": {"start": {"line": 55, "column": 14}, "end": {"line": 57, "column": 3}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 51, "column": 13}, "end": {"line": 51, "column": 16}}, "loc": {"start": {"line": 51, "column": 19}, "end": {"line": 51, "column": 25}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 51, "column": 27}, "end": {"line": 51, "column": 28}}, "loc": {"start": {"line": 51, "column": 39}, "end": {"line": 51, "column": 50}}}}, "branchMap": {"0": {"loc": {"start": {"line": 13, "column": 12}, "end": {"line": 13, "column": null}}, "type": "binary-expr", "locations": [{"start": {"line": 13, "column": 12}, "end": {"line": 13, "column": 20}}, {"start": {"line": 13, "column": 20}, "end": {"line": 13, "column": null}}]}}, "s": {"0": 11, "1": 11, "2": 11, "3": 11, "4": 11, "5": 11, "6": 11, "7": 0, "8": 11, "9": 11, "10": 11, "11": 11, "12": 11, "13": 11, "14": 11, "15": 11, "16": 11, "17": 11, "18": 0, "19": 0, "20": 11}, "f": {"0": 11, "1": 0, "2": 0, "3": 0}, "b": {"0": [11, 11]}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/tickets/tickets.concurrency.spec.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/tickets/tickets.concurrency.spec.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 54}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 48}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 37}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 51}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 51}}, "5": {"start": {"line": 6, "column": 0}, "end": {"line": 6, "column": 79}}, "6": {"start": {"line": 8, "column": 0}, "end": {"line": 224, "column": 3}}, "7": {"start": {"line": 13, "column": 2}, "end": {"line": 30, "column": 5}}, "8": {"start": {"line": 14, "column": 4}, "end": {"line": 26, "column": 17}}, "9": {"start": {"line": 28, "column": 4}, "end": {"line": 28, "column": 57}}, "10": {"start": {"line": 29, "column": 4}, "end": {"line": 29, "column": 52}}, "11": {"start": {"line": 32, "column": 2}, "end": {"line": 34, "column": 5}}, "12": {"start": {"line": 33, "column": 4}, "end": {"line": 33, "column": 25}}, "13": {"start": {"line": 36, "column": 2}, "end": {"line": 42, "column": 5}}, "14": {"start": {"line": 38, "column": 4}, "end": {"line": 38, "column": 50}}, "15": {"start": {"line": 39, "column": 4}, "end": {"line": 39, "column": 48}}, "16": {"start": {"line": 40, "column": 4}, "end": {"line": 40, "column": 48}}, "17": {"start": {"line": 41, "column": 4}, "end": {"line": 41, "column": 48}}, "18": {"start": {"line": 44, "column": 2}, "end": {"line": 223, "column": 5}}, "19": {"start": {"line": 45, "column": 4}, "end": {"line": 106, "column": 7}}, "20": {"start": {"line": 47, "column": 20}, "end": {"line": 53, "column": 8}}, "21": {"start": {"line": 55, "column": 20}, "end": {"line": 61, "column": 8}}, "22": {"start": {"line": 63, "column": 19}, "end": {"line": 72, "column": 8}}, "23": {"start": {"line": 74, "column": 19}, "end": {"line": 78, "column": 8}}, "24": {"start": {"line": 81, "column": 30}, "end": {"line": 84, "column": 8}}, "25": {"start": {"line": 87, "column": 22}, "end": {"line": 87, "column": 63}}, "26": {"start": {"line": 89, "column": 27}, "end": {"line": 89, "column": 79}}, "27": {"start": {"line": 89, "column": 47}, "end": {"line": 89, "column": 71}}, "28": {"start": {"line": 90, "column": 27}, "end": {"line": 90, "column": 78}}, "29": {"start": {"line": 90, "column": 47}, "end": {"line": 90, "column": 70}}, "30": {"start": {"line": 92, "column": 6}, "end": {"line": 92, "column": 35}}, "31": {"start": {"line": 93, "column": 6}, "end": {"line": 93, "column": 35}}, "32": {"start": {"line": 96, "column": 29}, "end": {"line": 96, "column": 96}}, "33": {"start": {"line": 96, "column": 47}, "end": {"line": 96, "column": 70}}, "34": {"start": {"line": 97, "column": 6}, "end": {"line": 97, "column": 70}}, "35": {"start": {"line": 100, "column": 26}, "end": {"line": 100, "column": 98}}, "36": {"start": {"line": 101, "column": 6}, "end": {"line": 101, "column": 47}}, "37": {"start": {"line": 104, "column": 26}, "end": {"line": 104, "column": 98}}, "38": {"start": {"line": 105, "column": 6}, "end": {"line": 105, "column": 50}}, "39": {"start": {"line": 108, "column": 4}, "end": {"line": 183, "column": 7}}, "40": {"start": {"line": 110, "column": 20}, "end": {"line": 116, "column": 8}}, "41": {"start": {"line": 118, "column": 20}, "end": {"line": 124, "column": 8}}, "42": {"start": {"line": 126, "column": 20}, "end": {"line": 132, "column": 8}}, "43": {"start": {"line": 134, "column": 20}, "end": {"line": 134, "column": 41}}, "44": {"start": {"line": 136, "column": 19}, "end": {"line": 145, "column": 8}}, "45": {"start": {"line": 147, "column": 20}, "end": {"line": 163, "column": 8}}, "46": {"start": {"line": 166, "column": 30}, "end": {"line": 167, "column": null}}, "47": {"start": {"line": 167, "column": 8}, "end": {"line": 167, "column": 81}}, "48": {"start": {"line": 171, "column": 22}, "end": {"line": 171, "column": 63}}, "49": {"start": {"line": 173, "column": 27}, "end": {"line": 173, "column": 79}}, "50": {"start": {"line": 173, "column": 47}, "end": {"line": 173, "column": 71}}, "51": {"start": {"line": 174, "column": 6}, "end": {"line": 174, "column": 35}}, "52": {"start": {"line": 177, "column": 27}, "end": {"line": 177, "column": 100}}, "53": {"start": {"line": 178, "column": 6}, "end": {"line": 178, "column": 67}}, "54": {"start": {"line": 178, "column": 40}, "end": {"line": 178, "column": 53}}, "55": {"start": {"line": 181, "column": 26}, "end": {"line": 181, "column": 98}}, "56": {"start": {"line": 182, "column": 6}, "end": {"line": 182, "column": 50}}, "57": {"start": {"line": 185, "column": 4}, "end": {"line": 222, "column": 7}}, "58": {"start": {"line": 187, "column": 19}, "end": {"line": 193, "column": 8}}, "59": {"start": {"line": 195, "column": 19}, "end": {"line": 204, "column": 8}}, "60": {"start": {"line": 206, "column": 19}, "end": {"line": 210, "column": 8}}, "61": {"start": {"line": 213, "column": 26}, "end": {"line": 213, "column": 98}}, "62": {"start": {"line": 214, "column": 6}, "end": {"line": 214, "column": 49}}, "63": {"start": {"line": 217, "column": 6}, "end": {"line": 217, "column": 78}}, "64": {"start": {"line": 220, "column": 26}, "end": {"line": 220, "column": 98}}, "65": {"start": {"line": 221, "column": 6}, "end": {"line": 221, "column": 78}}}, "fnMap": {"0": {"name": "(anonymous_0)", "decl": {"start": {"line": 8, "column": 47}, "end": {"line": 8, "column": 50}}, "loc": {"start": {"line": 8, "column": 52}, "end": {"line": 224, "column": 1}}}, "1": {"name": "(anonymous_1)", "decl": {"start": {"line": 13, "column": 12}, "end": {"line": 13, "column": 17}}, "loc": {"start": {"line": 13, "column": 23}, "end": {"line": 30, "column": 3}}}, "2": {"name": "(anonymous_2)", "decl": {"start": {"line": 32, "column": 11}, "end": {"line": 32, "column": 16}}, "loc": {"start": {"line": 32, "column": 22}, "end": {"line": 34, "column": 3}}}, "3": {"name": "(anonymous_3)", "decl": {"start": {"line": 36, "column": 13}, "end": {"line": 36, "column": 18}}, "loc": {"start": {"line": 36, "column": 24}, "end": {"line": 42, "column": 3}}}, "4": {"name": "(anonymous_4)", "decl": {"start": {"line": 44, "column": 44}, "end": {"line": 44, "column": 47}}, "loc": {"start": {"line": 44, "column": 49}, "end": {"line": 223, "column": 3}}}, "5": {"name": "(anonymous_5)", "decl": {"start": {"line": 45, "column": 65}, "end": {"line": 45, "column": 70}}, "loc": {"start": {"line": 45, "column": 76}, "end": {"line": 106, "column": 5}}}, "6": {"name": "(anonymous_6)", "decl": {"start": {"line": 89, "column": 42}, "end": {"line": 89, "column": 43}}, "loc": {"start": {"line": 89, "column": 47}, "end": {"line": 89, "column": 71}}}, "7": {"name": "(anonymous_7)", "decl": {"start": {"line": 90, "column": 42}, "end": {"line": 90, "column": 43}}, "loc": {"start": {"line": 90, "column": 47}, "end": {"line": 90, "column": 70}}}, "8": {"name": "(anonymous_8)", "decl": {"start": {"line": 96, "column": 42}, "end": {"line": 96, "column": 43}}, "loc": {"start": {"line": 96, "column": 47}, "end": {"line": 96, "column": 70}}}, "9": {"name": "(anonymous_9)", "decl": {"start": {"line": 108, "column": 73}, "end": {"line": 108, "column": 78}}, "loc": {"start": {"line": 108, "column": 84}, "end": {"line": 183, "column": 5}}}, "10": {"name": "(anonymous_10)", "decl": {"start": {"line": 166, "column": 40}, "end": {"line": 166, "column": 41}}, "loc": {"start": {"line": 167, "column": 8}, "end": {"line": 167, "column": 81}}}, "11": {"name": "(anonymous_11)", "decl": {"start": {"line": 173, "column": 42}, "end": {"line": 173, "column": 43}}, "loc": {"start": {"line": 173, "column": 47}, "end": {"line": 173, "column": 71}}}, "12": {"name": "(anonymous_12)", "decl": {"start": {"line": 178, "column": 32}, "end": {"line": 178, "column": 36}}, "loc": {"start": {"line": 178, "column": 40}, "end": {"line": 178, "column": 53}}}, "13": {"name": "(anonymous_13)", "decl": {"start": {"line": 185, "column": 66}, "end": {"line": 185, "column": 71}}, "loc": {"start": {"line": 185, "column": 77}, "end": {"line": 222, "column": 5}}}}, "branchMap": {"0": {"loc": {"start": {"line": 221, "column": 51}, "end": {"line": 221, "column": 76}}, "type": "binary-expr", "locations": [{"start": {"line": 221, "column": 51}, "end": {"line": 221, "column": 71}}, {"start": {"line": 221, "column": 75}, "end": {"line": 221, "column": 76}}]}}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 0, "18": 0, "19": 0, "20": 0, "21": 0, "22": 0, "23": 0, "24": 0, "25": 0, "26": 0, "27": 0, "28": 0, "29": 0, "30": 0, "31": 0, "32": 0, "33": 0, "34": 0, "35": 0, "36": 0, "37": 0, "38": 0, "39": 0, "40": 0, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 0, "61": 0, "62": 0, "63": 0, "64": 0, "65": 0}, "f": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0, "11": 0, "12": 0, "13": 0}, "b": {"0": [0, 0]}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/tickets/tickets.controller.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/tickets/tickets.controller.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": null}}, "1": {"start": {"line": 12, "column": 0}, "end": {"line": 12, "column": 94}}, "2": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 51}}, "3": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 60}}, "4": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 61}}, "5": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 56}}, "6": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 72}}, "7": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 59}}, "8": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 45}}, "9": {"start": {"line": 25, "column": 7}, "end": {"line": 72, "column": null}}, "10": {"start": {"line": 26, "column": 31}, "end": {"line": 26, "column": 47}}, "11": {"start": {"line": 34, "column": 4}, "end": {"line": 34, "column": 68}}, "12": {"start": {"line": 43, "column": 4}, "end": {"line": 43, "column": 57}}, "13": {"start": {"line": 50, "column": 4}, "end": {"line": 50, "column": 56}}, "14": {"start": {"line": 62, "column": 4}, "end": {"line": 62, "column": 52}}, "15": {"start": {"line": 70, "column": 4}, "end": {"line": 70, "column": 43}}, "16": {"start": {"line": 25, "column": 13}, "end": {"line": 25, "column": 30}}, "17": {"start": {"line": 33, "column": 8}, "end": {"line": 35, "column": null}}, "18": {"start": {"line": 42, "column": 8}, "end": {"line": 44, "column": null}}, "19": {"start": {"line": 49, "column": 8}, "end": {"line": 51, "column": null}}, "20": {"start": {"line": 61, "column": 8}, "end": {"line": 63, "column": null}}, "21": {"start": {"line": 69, "column": 8}, "end": {"line": 71, "column": null}}, "22": {"start": {"line": 25, "column": 13}, "end": {"line": 72, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 26, "column": 2}, "end": {"line": 26, "column": 31}}, "loc": {"start": {"line": 26, "column": 61}, "end": {"line": 26, "column": 65}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 33, "column": 2}, "end": {"line": 33, "column": 7}}, "loc": {"start": {"line": 33, "column": 86}, "end": {"line": 35, "column": 3}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 42, "column": 2}, "end": {"line": 42, "column": 7}}, "loc": {"start": {"line": 42, "column": 86}, "end": {"line": 44, "column": 3}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 49, "column": 2}, "end": {"line": 49, "column": 7}}, "loc": {"start": {"line": 49, "column": 47}, "end": {"line": 51, "column": 3}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 61, "column": 2}, "end": {"line": 61, "column": 7}}, "loc": {"start": {"line": 61, "column": 76}, "end": {"line": 63, "column": 3}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 69, "column": 2}, "end": {"line": 69, "column": 7}}, "loc": {"start": {"line": 69, "column": 54}, "end": {"line": 71, "column": 3}}}}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 1, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 1, "17": 1, "18": 1, "19": 1, "20": 1, "21": 1, "22": 1}, "f": {"0": 1, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0}, "b": {}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/tickets/tickets.module.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/tickets/tickets.module.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 40}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 48}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 51}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 57}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 55}}, "5": {"start": {"line": 13, "column": 7}, "end": {"line": 13, "column": null}}, "6": {"start": {"line": 13, "column": 13}, "end": {"line": 13, "column": 26}}, "7": {"start": {"line": 13, "column": 13}, "end": {"line": 13, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "f": {}, "b": {}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/tickets/tickets.service.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/tickets/tickets.service.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 103}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 49}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 69}}, "4": {"start": {"line": 8, "column": 7}, "end": {"line": 184, "column": null}}, "5": {"start": {"line": 11, "column": 21}, "end": {"line": 11, "column": 39}}, "6": {"start": {"line": 13, "column": 21}, "end": {"line": 13, "column": 37}}, "7": {"start": {"line": 15, "column": 21}, "end": {"line": 15, "column": 37}}, "8": {"start": {"line": 17, "column": 21}, "end": {"line": 17, "column": 37}}, "9": {"start": {"line": 18, "column": 21}, "end": {"line": 18, "column": 33}}, "10": {"start": {"line": 22, "column": 31}, "end": {"line": 22, "column": 46}}, "11": {"start": {"line": 24, "column": 4}, "end": {"line": 93, "column": 7}}, "12": {"start": {"line": 26, "column": 19}, "end": {"line": 29, "column": 8}}, "13": {"start": {"line": 31, "column": 6}, "end": {"line": 33, "column": 7}}, "14": {"start": {"line": 32, "column": 8}, "end": {"line": 32, "column": 68}}, "15": {"start": {"line": 35, "column": 6}, "end": {"line": 37, "column": 7}}, "16": {"start": {"line": 36, "column": 8}, "end": {"line": 36, "column": 62}}, "17": {"start": {"line": 40, "column": 19}, "end": {"line": 43, "column": 8}}, "18": {"start": {"line": 45, "column": 6}, "end": {"line": 47, "column": 7}}, "19": {"start": {"line": 46, "column": 8}, "end": {"line": 46, "column": 54}}, "20": {"start": {"line": 49, "column": 6}, "end": {"line": 51, "column": 7}}, "21": {"start": {"line": 50, "column": 8}, "end": {"line": 50, "column": 71}}, "22": {"start": {"line": 54, "column": 6}, "end": {"line": 56, "column": 7}}, "23": {"start": {"line": 55, "column": 8}, "end": {"line": 55, "column": 76}}, "24": {"start": {"line": 59, "column": 19}, "end": {"line": 59, "column": 73}}, "25": {"start": {"line": 60, "column": 6}, "end": {"line": 62, "column": 7}}, "26": {"start": {"line": 61, "column": 8}, "end": {"line": 61, "column": 54}}, "27": {"start": {"line": 65, "column": 29}, "end": {"line": 67, "column": 8}}, "28": {"start": {"line": 69, "column": 6}, "end": {"line": 71, "column": 7}}, "29": {"start": {"line": 70, "column": 8}, "end": {"line": 70, "column": 80}}, "30": {"start": {"line": 74, "column": 21}, "end": {"line": 80, "column": 8}}, "31": {"start": {"line": 82, "column": 26}, "end": {"line": 82, "column": 60}}, "32": {"start": {"line": 85, "column": 6}, "end": {"line": 85, "column": 27}}, "33": {"start": {"line": 86, "column": 6}, "end": {"line": 86, "column": 37}}, "34": {"start": {"line": 89, "column": 6}, "end": {"line": 89, "column": 31}}, "35": {"start": {"line": 90, "column": 6}, "end": {"line": 90, "column": 37}}, "36": {"start": {"line": 92, "column": 6}, "end": {"line": 92, "column": 25}}, "37": {"start": {"line": 97, "column": 4}, "end": {"line": 150, "column": 7}}, "38": {"start": {"line": 99, "column": 21}, "end": {"line": 103, "column": 8}}, "39": {"start": {"line": 105, "column": 6}, "end": {"line": 107, "column": 7}}, "40": {"start": {"line": 106, "column": 8}, "end": {"line": 106, "column": 56}}, "41": {"start": {"line": 109, "column": 6}, "end": {"line": 111, "column": 7}}, "42": {"start": {"line": 110, "column": 8}, "end": {"line": 110, "column": 69}}, "43": {"start": {"line": 114, "column": 18}, "end": {"line": 114, "column": 28}}, "44": {"start": {"line": 115, "column": 28}, "end": {"line": 115, "column": 63}}, "45": {"start": {"line": 116, "column": 29}, "end": {"line": 116, "column": 68}}, "46": {"start": {"line": 117, "column": 34}, "end": {"line": 117, "column": 67}}, "47": {"start": {"line": 119, "column": 6}, "end": {"line": 121, "column": 7}}, "48": {"start": {"line": 120, "column": 8}, "end": {"line": 120, "column": 97}}, "49": {"start": {"line": 124, "column": 6}, "end": {"line": 124, "column": 45}}, "50": {"start": {"line": 125, "column": 28}, "end": {"line": 125, "column": 62}}, "51": {"start": {"line": 128, "column": 19}, "end": {"line": 131, "column": 8}}, "52": {"start": {"line": 133, "column": 6}, "end": {"line": 136, "column": 7}}, "53": {"start": {"line": 134, "column": 8}, "end": {"line": 134, "column": 30}}, "54": {"start": {"line": 135, "column": 8}, "end": {"line": 135, "column": 39}}, "55": {"start": {"line": 139, "column": 19}, "end": {"line": 142, "column": 8}}, "56": {"start": {"line": 144, "column": 6}, "end": {"line": 147, "column": 7}}, "57": {"start": {"line": 145, "column": 8}, "end": {"line": 145, "column": 33}}, "58": {"start": {"line": 146, "column": 8}, "end": {"line": 146, "column": 39}}, "59": {"start": {"line": 149, "column": 6}, "end": {"line": 149, "column": 27}}, "60": {"start": {"line": 154, "column": 4}, "end": {"line": 158, "column": 7}}, "61": {"start": {"line": 162, "column": 19}, "end": {"line": 165, "column": 6}}, "62": {"start": {"line": 167, "column": 4}, "end": {"line": 169, "column": 5}}, "63": {"start": {"line": 168, "column": 6}, "end": {"line": 168, "column": 54}}, "64": {"start": {"line": 171, "column": 4}, "end": {"line": 171, "column": 18}}, "65": {"start": {"line": 175, "column": 29}, "end": {"line": 180, "column": 6}}, "66": {"start": {"line": 182, "column": 4}, "end": {"line": 182, "column": 43}}, "67": {"start": {"line": 8, "column": 13}, "end": {"line": 8, "column": 27}}, "68": {"start": {"line": 8, "column": 13}, "end": {"line": 184, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 9, "column": 2}, "end": {"line": 9, "column": null}}, "loc": {"start": {"line": 18, "column": 43}, "end": {"line": 19, "column": 6}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 21, "column": 2}, "end": {"line": 21, "column": 7}}, "loc": {"start": {"line": 21, "column": 67}, "end": {"line": 94, "column": 3}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 24, "column": 45}, "end": {"line": 24, "column": 50}}, "loc": {"start": {"line": 24, "column": 63}, "end": {"line": 93, "column": 5}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 96, "column": 2}, "end": {"line": 96, "column": 7}}, "loc": {"start": {"line": 96, "column": 53}, "end": {"line": 151, "column": 3}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 97, "column": 45}, "end": {"line": 97, "column": 50}}, "loc": {"start": {"line": 97, "column": 63}, "end": {"line": 150, "column": 5}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 153, "column": 2}, "end": {"line": 153, "column": 7}}, "loc": {"start": {"line": 153, "column": 38}, "end": {"line": 159, "column": 3}}}, "6": {"name": "(anonymous_10)", "decl": {"start": {"line": 161, "column": 2}, "end": {"line": 161, "column": 7}}, "loc": {"start": {"line": 161, "column": 26}, "end": {"line": 172, "column": 3}}}, "7": {"name": "(anonymous_11)", "decl": {"start": {"line": 174, "column": 2}, "end": {"line": 174, "column": 7}}, "loc": {"start": {"line": 174, "column": 52}, "end": {"line": 183, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 31, "column": 6}, "end": {"line": 33, "column": 7}}, "type": "if", "locations": [{"start": {"line": 31, "column": 6}, "end": {"line": 33, "column": 7}}]}, "1": {"loc": {"start": {"line": 35, "column": 6}, "end": {"line": 37, "column": 7}}, "type": "if", "locations": [{"start": {"line": 35, "column": 6}, "end": {"line": 37, "column": 7}}]}, "2": {"loc": {"start": {"line": 45, "column": 6}, "end": {"line": 47, "column": 7}}, "type": "if", "locations": [{"start": {"line": 45, "column": 6}, "end": {"line": 47, "column": 7}}]}, "3": {"loc": {"start": {"line": 49, "column": 6}, "end": {"line": 51, "column": 7}}, "type": "if", "locations": [{"start": {"line": 49, "column": 6}, "end": {"line": 51, "column": 7}}]}, "4": {"loc": {"start": {"line": 54, "column": 6}, "end": {"line": 56, "column": 7}}, "type": "if", "locations": [{"start": {"line": 54, "column": 6}, "end": {"line": 56, "column": 7}}]}, "5": {"loc": {"start": {"line": 60, "column": 6}, "end": {"line": 62, "column": 7}}, "type": "if", "locations": [{"start": {"line": 60, "column": 6}, "end": {"line": 62, "column": 7}}]}, "6": {"loc": {"start": {"line": 69, "column": 6}, "end": {"line": 71, "column": 7}}, "type": "if", "locations": [{"start": {"line": 69, "column": 6}, "end": {"line": 71, "column": 7}}]}, "7": {"loc": {"start": {"line": 105, "column": 6}, "end": {"line": 107, "column": 7}}, "type": "if", "locations": [{"start": {"line": 105, "column": 6}, "end": {"line": 107, "column": 7}}]}, "8": {"loc": {"start": {"line": 109, "column": 6}, "end": {"line": 111, "column": 7}}, "type": "if", "locations": [{"start": {"line": 109, "column": 6}, "end": {"line": 111, "column": 7}}]}, "9": {"loc": {"start": {"line": 119, "column": 6}, "end": {"line": 121, "column": 7}}, "type": "if", "locations": [{"start": {"line": 119, "column": 6}, "end": {"line": 121, "column": 7}}]}, "10": {"loc": {"start": {"line": 133, "column": 6}, "end": {"line": 136, "column": 7}}, "type": "if", "locations": [{"start": {"line": 133, "column": 6}, "end": {"line": 136, "column": 7}}]}, "11": {"loc": {"start": {"line": 144, "column": 6}, "end": {"line": 147, "column": 7}}, "type": "if", "locations": [{"start": {"line": 144, "column": 6}, "end": {"line": 147, "column": 7}}]}, "12": {"loc": {"start": {"line": 167, "column": 4}, "end": {"line": 169, "column": 5}}, "type": "if", "locations": [{"start": {"line": 167, "column": 4}, "end": {"line": 169, "column": 5}}]}, "13": {"loc": {"start": {"line": 174, "column": 16}, "end": {"line": 174, "column": 32}}, "type": "default-arg", "locations": [{"start": {"line": 174, "column": 31}, "end": {"line": 174, "column": 32}}]}, "14": {"loc": {"start": {"line": 174, "column": 34}, "end": {"line": 174, "column": 52}}, "type": "default-arg", "locations": [{"start": {"line": 174, "column": 50}, "end": {"line": 174, "column": 52}}]}}, "s": {"0": 2, "1": 2, "2": 2, "3": 2, "4": 2, "5": 8, "6": 8, "7": 8, "8": 8, "9": 8, "10": 3, "11": 3, "12": 3, "13": 3, "14": 1, "15": 2, "16": 1, "17": 1, "18": 1, "19": 0, "20": 1, "21": 0, "22": 1, "23": 0, "24": 1, "25": 1, "26": 0, "27": 1, "28": 1, "29": 0, "30": 1, "31": 1, "32": 1, "33": 1, "34": 1, "35": 1, "36": 1, "37": 2, "38": 2, "39": 2, "40": 0, "41": 2, "42": 0, "43": 2, "44": 2, "45": 2, "46": 2, "47": 2, "48": 1, "49": 1, "50": 1, "51": 1, "52": 1, "53": 1, "54": 1, "55": 1, "56": 1, "57": 1, "58": 1, "59": 1, "60": 1, "61": 2, "62": 2, "63": 1, "64": 1, "65": 0, "66": 0, "67": 2, "68": 2}, "f": {"0": 8, "1": 3, "2": 3, "3": 2, "4": 2, "5": 1, "6": 2, "7": 0}, "b": {"0": [1], "1": [1], "2": [0], "3": [0], "4": [0], "5": [0], "6": [0], "7": [0], "8": [0], "9": [1], "10": [1], "11": [1], "12": [1], "13": [0], "14": [0]}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/trips/trips.controller.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/trips/trips.controller.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": null}}, "1": {"start": {"line": 13, "column": 0}, "end": {"line": 13, "column": 94}}, "2": {"start": {"line": 14, "column": 0}, "end": {"line": 14, "column": 47}}, "3": {"start": {"line": 15, "column": 0}, "end": {"line": 15, "column": 87}}, "4": {"start": {"line": 16, "column": 0}, "end": {"line": 16, "column": 61}}, "5": {"start": {"line": 17, "column": 0}, "end": {"line": 17, "column": 56}}, "6": {"start": {"line": 18, "column": 0}, "end": {"line": 18, "column": 59}}, "7": {"start": {"line": 19, "column": 0}, "end": {"line": 19, "column": 39}}, "8": {"start": {"line": 23, "column": 7}, "end": {"line": 93, "column": null}}, "9": {"start": {"line": 24, "column": 31}, "end": {"line": 24, "column": 45}}, "10": {"start": {"line": 35, "column": 4}, "end": {"line": 35, "column": 47}}, "11": {"start": {"line": 46, "column": 4}, "end": {"line": 46, "column": 51}}, "12": {"start": {"line": 58, "column": 4}, "end": {"line": 58, "column": 50}}, "13": {"start": {"line": 66, "column": 4}, "end": {"line": 66, "column": 41}}, "14": {"start": {"line": 78, "column": 4}, "end": {"line": 78, "column": 55}}, "15": {"start": {"line": 90, "column": 4}, "end": {"line": 90, "column": 39}}, "16": {"start": {"line": 91, "column": 4}, "end": {"line": 91, "column": 52}}, "17": {"start": {"line": 23, "column": 13}, "end": {"line": 23, "column": 28}}, "18": {"start": {"line": 34, "column": 8}, "end": {"line": 36, "column": null}}, "19": {"start": {"line": 45, "column": 8}, "end": {"line": 47, "column": null}}, "20": {"start": {"line": 57, "column": 8}, "end": {"line": 59, "column": null}}, "21": {"start": {"line": 65, "column": 8}, "end": {"line": 67, "column": null}}, "22": {"start": {"line": 77, "column": 8}, "end": {"line": 79, "column": null}}, "23": {"start": {"line": 89, "column": 8}, "end": {"line": 92, "column": null}}, "24": {"start": {"line": 23, "column": 13}, "end": {"line": 93, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 24, "column": 2}, "end": {"line": 24, "column": 31}}, "loc": {"start": {"line": 24, "column": 57}, "end": {"line": 24, "column": 61}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 34, "column": 2}, "end": {"line": 34, "column": 7}}, "loc": {"start": {"line": 34, "column": 49}, "end": {"line": 36, "column": 3}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 45, "column": 2}, "end": {"line": 45, "column": 7}}, "loc": {"start": {"line": 45, "column": 51}, "end": {"line": 47, "column": 3}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 57, "column": 2}, "end": {"line": 57, "column": 7}}, "loc": {"start": {"line": 57, "column": 76}, "end": {"line": 59, "column": 3}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 65, "column": 2}, "end": {"line": 65, "column": 7}}, "loc": {"start": {"line": 65, "column": 54}, "end": {"line": 67, "column": 3}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 77, "column": 2}, "end": {"line": 77, "column": 7}}, "loc": {"start": {"line": 77, "column": 91}, "end": {"line": 79, "column": 3}}}, "6": {"name": "(anonymous_10)", "decl": {"start": {"line": 89, "column": 2}, "end": {"line": 89, "column": 7}}, "loc": {"start": {"line": 89, "column": 53}, "end": {"line": 92, "column": 3}}}}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 1, "7": 1, "8": 1, "9": 1, "10": 0, "11": 0, "12": 0, "13": 0, "14": 0, "15": 0, "16": 0, "17": 1, "18": 1, "19": 1, "20": 1, "21": 1, "22": 1, "23": 1, "24": 1}, "f": {"0": 1, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "b": {}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/trips/trips.module.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/trips/trips.module.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 40}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 48}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 47}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 53}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 49}}, "5": {"start": {"line": 13, "column": 7}, "end": {"line": 13, "column": null}}, "6": {"start": {"line": 13, "column": 13}, "end": {"line": 13, "column": 24}}, "7": {"start": {"line": 13, "column": 13}, "end": {"line": 13, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0, "7": 0}, "f": {}, "b": {}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/trips/trips.service.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/trips/trips.service.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 84}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 51}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 63}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 49}}, "4": {"start": {"line": 8, "column": 7}, "end": {"line": 169, "column": null}}, "5": {"start": {"line": 11, "column": 21}, "end": {"line": 11, "column": 37}}, "6": {"start": {"line": 13, "column": 21}, "end": {"line": 13, "column": 37}}, "7": {"start": {"line": 15, "column": 21}, "end": {"line": 15, "column": 39}}, "8": {"start": {"line": 19, "column": 78}, "end": {"line": 19, "column": 91}}, "9": {"start": {"line": 22, "column": 19}, "end": {"line": 22, "column": 83}}, "10": {"start": {"line": 23, "column": 4}, "end": {"line": 25, "column": 5}}, "11": {"start": {"line": 24, "column": 6}, "end": {"line": 24, "column": 54}}, "12": {"start": {"line": 28, "column": 22}, "end": {"line": 28, "column": 45}}, "13": {"start": {"line": 29, "column": 20}, "end": {"line": 29, "column": 41}}, "14": {"start": {"line": 30, "column": 4}, "end": {"line": 32, "column": 5}}, "15": {"start": {"line": 31, "column": 6}, "end": {"line": 31, "column": 82}}, "16": {"start": {"line": 35, "column": 4}, "end": {"line": 37, "column": 5}}, "17": {"start": {"line": 36, "column": 6}, "end": {"line": 36, "column": 76}}, "18": {"start": {"line": 40, "column": 17}, "end": {"line": 47, "column": 6}}, "19": {"start": {"line": 49, "column": 22}, "end": {"line": 49, "column": 58}}, "20": {"start": {"line": 52, "column": 26}, "end": {"line": 52, "column": 28}}, "21": {"start": {"line": 53, "column": 4}, "end": {"line": 60, "column": 5}}, "22": {"start": {"line": 53, "column": 17}, "end": {"line": 53, "column": 18}}, "23": {"start": {"line": 54, "column": 19}, "end": {"line": 58, "column": 8}}, "24": {"start": {"line": 59, "column": 6}, "end": {"line": 59, "column": 23}}, "25": {"start": {"line": 62, "column": 4}, "end": {"line": 62, "column": 42}}, "26": {"start": {"line": 64, "column": 4}, "end": {"line": 64, "column": 38}}, "27": {"start": {"line": 68, "column": 27}, "end": {"line": 73, "column": 6}}, "28": {"start": {"line": 75, "column": 4}, "end": {"line": 75, "column": 41}}, "29": {"start": {"line": 79, "column": 17}, "end": {"line": 82, "column": 6}}, "30": {"start": {"line": 84, "column": 4}, "end": {"line": 86, "column": 5}}, "31": {"start": {"line": 85, "column": 6}, "end": {"line": 85, "column": 52}}, "32": {"start": {"line": 88, "column": 4}, "end": {"line": 88, "column": 16}}, "33": {"start": {"line": 92, "column": 67}, "end": {"line": 92, "column": 76}}, "34": {"start": {"line": 95, "column": 23}, "end": {"line": 95, "column": 37}}, "35": {"start": {"line": 96, "column": 23}, "end": {"line": 96, "column": 43}}, "36": {"start": {"line": 97, "column": 4}, "end": {"line": 97, "column": 36}}, "37": {"start": {"line": 98, "column": 21}, "end": {"line": 98, "column": 41}}, "38": {"start": {"line": 99, "column": 4}, "end": {"line": 99, "column": 39}}, "39": {"start": {"line": 101, "column": 27}, "end": {"line": 112, "column": 6}}, "40": {"start": {"line": 114, "column": 4}, "end": {"line": 114, "column": 41}}, "41": {"start": {"line": 118, "column": 17}, "end": {"line": 118, "column": 39}}, "42": {"start": {"line": 121, "column": 4}, "end": {"line": 126, "column": 5}}, "43": {"start": {"line": 122, "column": 21}, "end": {"line": 122, "column": 99}}, "44": {"start": {"line": 123, "column": 6}, "end": {"line": 125, "column": 7}}, "45": {"start": {"line": 124, "column": 8}, "end": {"line": 124, "column": 56}}, "46": {"start": {"line": 129, "column": 4}, "end": {"line": 140, "column": 5}}, "47": {"start": {"line": 130, "column": 28}, "end": {"line": 130, "column": 116}}, "48": {"start": {"line": 131, "column": 26}, "end": {"line": 131, "column": 108}}, "49": {"start": {"line": 133, "column": 6}, "end": {"line": 135, "column": 7}}, "50": {"start": {"line": 134, "column": 8}, "end": {"line": 134, "column": 84}}, "51": {"start": {"line": 137, "column": 6}, "end": {"line": 139, "column": 7}}, "52": {"start": {"line": 138, "column": 8}, "end": {"line": 138, "column": 78}}, "53": {"start": {"line": 143, "column": 4}, "end": {"line": 143, "column": 39}}, "54": {"start": {"line": 144, "column": 4}, "end": {"line": 146, "column": 5}}, "55": {"start": {"line": 145, "column": 6}, "end": {"line": 145, "column": 65}}, "56": {"start": {"line": 147, "column": 4}, "end": {"line": 149, "column": 5}}, "57": {"start": {"line": 148, "column": 6}, "end": {"line": 148, "column": 61}}, "58": {"start": {"line": 151, "column": 4}, "end": {"line": 151, "column": 41}}, "59": {"start": {"line": 152, "column": 4}, "end": {"line": 152, "column": 28}}, "60": {"start": {"line": 156, "column": 17}, "end": {"line": 156, "column": 39}}, "61": {"start": {"line": 159, "column": 24}, "end": {"line": 161, "column": 6}}, "62": {"start": {"line": 163, "column": 4}, "end": {"line": 165, "column": 5}}, "63": {"start": {"line": 164, "column": 6}, "end": {"line": 164, "column": 81}}, "64": {"start": {"line": 167, "column": 4}, "end": {"line": 167, "column": 43}}, "65": {"start": {"line": 8, "column": 13}, "end": {"line": 8, "column": 25}}, "66": {"start": {"line": 8, "column": 13}, "end": {"line": 169, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 9, "column": 2}, "end": {"line": 9, "column": null}}, "loc": {"start": {"line": 15, "column": 57}, "end": {"line": 16, "column": 6}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 18, "column": 2}, "end": {"line": 18, "column": 7}}, "loc": {"start": {"line": 18, "column": 43}, "end": {"line": 65, "column": 3}}}, "2": {"name": "(anonymous_6)", "decl": {"start": {"line": 67, "column": 2}, "end": {"line": 67, "column": 7}}, "loc": {"start": {"line": 67, "column": 52}, "end": {"line": 76, "column": 3}}}, "3": {"name": "(anonymous_7)", "decl": {"start": {"line": 78, "column": 2}, "end": {"line": 78, "column": 7}}, "loc": {"start": {"line": 78, "column": 26}, "end": {"line": 89, "column": 3}}}, "4": {"name": "(anonymous_8)", "decl": {"start": {"line": 91, "column": 2}, "end": {"line": 91, "column": 7}}, "loc": {"start": {"line": 91, "column": 40}, "end": {"line": 115, "column": 3}}}, "5": {"name": "(anonymous_9)", "decl": {"start": {"line": 117, "column": 2}, "end": {"line": 117, "column": 7}}, "loc": {"start": {"line": 117, "column": 55}, "end": {"line": 153, "column": 3}}}, "6": {"name": "(anonymous_10)", "decl": {"start": {"line": 155, "column": 2}, "end": {"line": 155, "column": 7}}, "loc": {"start": {"line": 155, "column": 25}, "end": {"line": 168, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 23, "column": 4}, "end": {"line": 25, "column": 5}}, "type": "if", "locations": [{"start": {"line": 23, "column": 4}, "end": {"line": 25, "column": 5}}]}, "1": {"loc": {"start": {"line": 30, "column": 4}, "end": {"line": 32, "column": 5}}, "type": "if", "locations": [{"start": {"line": 30, "column": 4}, "end": {"line": 32, "column": 5}}]}, "2": {"loc": {"start": {"line": 35, "column": 4}, "end": {"line": 37, "column": 5}}, "type": "if", "locations": [{"start": {"line": 35, "column": 4}, "end": {"line": 37, "column": 5}}]}, "3": {"loc": {"start": {"line": 67, "column": 16}, "end": {"line": 67, "column": 32}}, "type": "default-arg", "locations": [{"start": {"line": 67, "column": 31}, "end": {"line": 67, "column": 32}}]}, "4": {"loc": {"start": {"line": 67, "column": 34}, "end": {"line": 67, "column": 52}}, "type": "default-arg", "locations": [{"start": {"line": 67, "column": 50}, "end": {"line": 67, "column": 52}}]}, "5": {"loc": {"start": {"line": 84, "column": 4}, "end": {"line": 86, "column": 5}}, "type": "if", "locations": [{"start": {"line": 84, "column": 4}, "end": {"line": 86, "column": 5}}]}, "6": {"loc": {"start": {"line": 92, "column": 42}, "end": {"line": 92, "column": 50}}, "type": "default-arg", "locations": [{"start": {"line": 92, "column": 49}, "end": {"line": 92, "column": 50}}]}, "7": {"loc": {"start": {"line": 92, "column": 52}, "end": {"line": 92, "column": 62}}, "type": "default-arg", "locations": [{"start": {"line": 92, "column": 60}, "end": {"line": 92, "column": 62}}]}, "8": {"loc": {"start": {"line": 121, "column": 4}, "end": {"line": 126, "column": 5}}, "type": "if", "locations": [{"start": {"line": 121, "column": 4}, "end": {"line": 126, "column": 5}}]}, "9": {"loc": {"start": {"line": 123, "column": 6}, "end": {"line": 125, "column": 7}}, "type": "if", "locations": [{"start": {"line": 123, "column": 6}, "end": {"line": 125, "column": 7}}]}, "10": {"loc": {"start": {"line": 129, "column": 4}, "end": {"line": 140, "column": 5}}, "type": "if", "locations": [{"start": {"line": 129, "column": 4}, "end": {"line": 140, "column": 5}}]}, "11": {"loc": {"start": {"line": 129, "column": 8}, "end": {"line": 129, "column": 64}}, "type": "binary-expr", "locations": [{"start": {"line": 129, "column": 8}, "end": {"line": 129, "column": 35}}, {"start": {"line": 129, "column": 39}, "end": {"line": 129, "column": 64}}]}, "12": {"loc": {"start": {"line": 130, "column": 28}, "end": {"line": 130, "column": 116}}, "type": "cond-expr", "locations": [{"start": {"line": 130, "column": 58}, "end": {"line": 130, "column": 95}}, {"start": {"line": 130, "column": 98}, "end": {"line": 130, "column": 116}}]}, "13": {"loc": {"start": {"line": 131, "column": 26}, "end": {"line": 131, "column": 108}}, "type": "cond-expr", "locations": [{"start": {"line": 131, "column": 54}, "end": {"line": 131, "column": 89}}, {"start": {"line": 131, "column": 92}, "end": {"line": 131, "column": 108}}]}, "14": {"loc": {"start": {"line": 133, "column": 6}, "end": {"line": 135, "column": 7}}, "type": "if", "locations": [{"start": {"line": 133, "column": 6}, "end": {"line": 135, "column": 7}}]}, "15": {"loc": {"start": {"line": 137, "column": 6}, "end": {"line": 139, "column": 7}}, "type": "if", "locations": [{"start": {"line": 137, "column": 6}, "end": {"line": 139, "column": 7}}]}, "16": {"loc": {"start": {"line": 144, "column": 4}, "end": {"line": 146, "column": 5}}, "type": "if", "locations": [{"start": {"line": 144, "column": 4}, "end": {"line": 146, "column": 5}}]}, "17": {"loc": {"start": {"line": 147, "column": 4}, "end": {"line": 149, "column": 5}}, "type": "if", "locations": [{"start": {"line": 147, "column": 4}, "end": {"line": 149, "column": 5}}]}, "18": {"loc": {"start": {"line": 163, "column": 4}, "end": {"line": 165, "column": 5}}, "type": "if", "locations": [{"start": {"line": 163, "column": 4}, "end": {"line": 165, "column": 5}}]}}, "s": {"0": 2, "1": 2, "2": 2, "3": 2, "4": 2, "5": 8, "6": 8, "7": 8, "8": 3, "9": 3, "10": 3, "11": 1, "12": 2, "13": 2, "14": 2, "15": 1, "16": 1, "17": 0, "18": 1, "19": 1, "20": 1, "21": 1, "22": 1, "23": 40, "24": 40, "25": 1, "26": 1, "27": 0, "28": 0, "29": 5, "30": 5, "31": 1, "32": 4, "33": 1, "34": 1, "35": 1, "36": 1, "37": 1, "38": 1, "39": 1, "40": 1, "41": 0, "42": 0, "43": 0, "44": 0, "45": 0, "46": 0, "47": 0, "48": 0, "49": 0, "50": 0, "51": 0, "52": 0, "53": 0, "54": 0, "55": 0, "56": 0, "57": 0, "58": 0, "59": 0, "60": 2, "61": 2, "62": 2, "63": 1, "64": 1, "65": 2, "66": 2}, "f": {"0": 8, "1": 3, "2": 0, "3": 5, "4": 1, "5": 0, "6": 2}, "b": {"0": [1], "1": [1], "2": [0], "3": [0], "4": [0], "5": [1], "6": [0], "7": [0], "8": [0], "9": [0], "10": [0], "11": [0, 0], "12": [0, 0], "13": [0, 0], "14": [0], "15": [0], "16": [0], "17": [0], "18": [1]}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/weather/weather.controller.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/weather/weather.controller.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 67}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 94}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 51}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 51}}, "4": {"start": {"line": 5, "column": 0}, "end": {"line": 5, "column": 44}}, "5": {"start": {"line": 10, "column": 7}, "end": {"line": 43, "column": null}}, "6": {"start": {"line": 11, "column": 31}, "end": {"line": 11, "column": 47}}, "7": {"start": {"line": 41, "column": 4}, "end": {"line": 41, "column": 54}}, "8": {"start": {"line": 10, "column": 13}, "end": {"line": 10, "column": 30}}, "9": {"start": {"line": 40, "column": 8}, "end": {"line": 42, "column": null}}, "10": {"start": {"line": 10, "column": 13}, "end": {"line": 43, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_4)", "decl": {"start": {"line": 11, "column": 2}, "end": {"line": 11, "column": 31}}, "loc": {"start": {"line": 11, "column": 61}, "end": {"line": 11, "column": 65}}}, "1": {"name": "(anonymous_5)", "decl": {"start": {"line": 40, "column": 2}, "end": {"line": 40, "column": 7}}, "loc": {"start": {"line": 40, "column": 46}, "end": {"line": 42, "column": 3}}}}, "branchMap": {}, "s": {"0": 1, "1": 1, "2": 1, "3": 1, "4": 1, "5": 1, "6": 2, "7": 2, "8": 1, "9": 1, "10": 1}, "f": {"0": 2, "1": 2}, "b": {}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/weather/weather.module.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/weather/weather.module.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 40}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 43}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 51}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 57}}, "4": {"start": {"line": 12, "column": 7}, "end": {"line": 12, "column": null}}, "5": {"start": {"line": 12, "column": 13}, "end": {"line": 12, "column": 26}}, "6": {"start": {"line": 12, "column": 13}, "end": {"line": 12, "column": null}}}, "fnMap": {}, "branchMap": {}, "s": {"0": 0, "1": 0, "2": 0, "3": 0, "4": 0, "5": 0, "6": 0}, "f": {}, "b": {}}, "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/weather/weather.service.ts": {"path": "/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/weather/weather.service.ts", "statementMap": {"0": {"start": {"line": 1, "column": 0}, "end": {"line": 1, "column": 102}}, "1": {"start": {"line": 2, "column": 0}, "end": {"line": 2, "column": 47}}, "2": {"start": {"line": 3, "column": 0}, "end": {"line": 3, "column": 45}}, "3": {"start": {"line": 4, "column": 0}, "end": {"line": 4, "column": 64}}, "4": {"start": {"line": 30, "column": 27}, "end": {"line": 140, "column": null}}, "5": {"start": {"line": 31, "column": 28}, "end": {"line": 31, "column": 59}}, "6": {"start": {"line": 34, "column": 34}, "end": {"line": 34, "column": 41}}, "7": {"start": {"line": 37, "column": 21}, "end": {"line": 37, "column": 36}}, "8": {"start": {"line": 38, "column": 21}, "end": {"line": 38, "column": 35}}, "9": {"start": {"line": 40, "column": 4}, "end": {"line": 40, "column": 75}}, "10": {"start": {"line": 41, "column": 4}, "end": {"line": 41, "column": 122}}, "11": {"start": {"line": 43, "column": 4}, "end": {"line": 45, "column": 5}}, "12": {"start": {"line": 44, "column": 6}, "end": {"line": 44, "column": 64}}, "13": {"start": {"line": 50, "column": 4}, "end": {"line": 52, "column": 5}}, "14": {"start": {"line": 51, "column": 6}, "end": {"line": 51, "column": 61}}, "15": {"start": {"line": 54, "column": 27}, "end": {"line": 54, "column": 52}}, "16": {"start": {"line": 55, "column": 21}, "end": {"line": 55, "column": 48}}, "17": {"start": {"line": 58, "column": 4}, "end": {"line": 66, "column": 5}}, "18": {"start": {"line": 59, "column": 28}, "end": {"line": 59, "column": 89}}, "19": {"start": {"line": 60, "column": 6}, "end": {"line": 63, "column": 7}}, "20": {"start": {"line": 61, "column": 8}, "end": {"line": 61, "column": 73}}, "21": {"start": {"line": 62, "column": 8}, "end": {"line": 62, "column": 54}}, "22": {"start": {"line": 65, "column": 6}, "end": {"line": 65, "column": 88}}, "23": {"start": {"line": 69, "column": 4}, "end": {"line": 71, "column": 5}}, "24": {"start": {"line": 70, "column": 6}, "end": {"line": 70, "column": 81}}, "25": {"start": {"line": 73, "column": 4}, "end": {"line": 113, "column": 5}}, "26": {"start": {"line": 74, "column": 62}, "end": {"line": 83, "column": null}}, "27": {"start": {"line": 86, "column": 26}, "end": {"line": 86, "column": 66}}, "28": {"start": {"line": 89, "column": 6}, "end": {"line": 94, "column": 7}}, "29": {"start": {"line": 90, "column": 8}, "end": {"line": 90, "column": 82}}, "30": {"start": {"line": 91, "column": 8}, "end": {"line": 91, "column": 92}}, "31": {"start": {"line": 93, "column": 8}, "end": {"line": 93, "column": 76}}, "32": {"start": {"line": 96, "column": 6}, "end": {"line": 96, "column": 47}}, "33": {"start": {"line": 98, "column": 6}, "end": {"line": 98, "column": 75}}, "34": {"start": {"line": 100, "column": 6}, "end": {"line": 110, "column": 7}}, "35": {"start": {"line": 101, "column": 8}, "end": {"line": 103, "column": 9}}, "36": {"start": {"line": 102, "column": 10}, "end": {"line": 102, "column": 68}}, "37": {"start": {"line": 104, "column": 8}, "end": {"line": 106, "column": 9}}, "38": {"start": {"line": 105, "column": 10}, "end": {"line": 105, "column": 89}}, "39": {"start": {"line": 107, "column": 8}, "end": {"line": 109, "column": 9}}, "40": {"start": {"line": 108, "column": 10}, "end": {"line": 108, "column": 87}}, "41": {"start": {"line": 112, "column": 6}, "end": {"line": 112, "column": 88}}, "42": {"start": {"line": 117, "column": 26}, "end": {"line": 117, "column": 62}}, "43": {"start": {"line": 119, "column": 4}, "end": {"line": 132, "column": 6}}, "44": {"start": {"line": 136, "column": 23}, "end": {"line": 136, "column": 123}}, "45": {"start": {"line": 137, "column": 18}, "end": {"line": 137, "column": 49}}, "46": {"start": {"line": 138, "column": 4}, "end": {"line": 138, "column": 29}}, "47": {"start": {"line": 30, "column": 13}, "end": {"line": 30, "column": 27}}, "48": {"start": {"line": 30, "column": 13}, "end": {"line": 140, "column": null}}}, "fnMap": {"0": {"name": "(anonymous_2)", "decl": {"start": {"line": 36, "column": 2}, "end": {"line": 36, "column": null}}, "loc": {"start": {"line": 38, "column": 47}, "end": {"line": 46, "column": 3}}}, "1": {"name": "(anonymous_3)", "decl": {"start": {"line": 48, "column": 2}, "end": {"line": 48, "column": 7}}, "loc": {"start": {"line": 48, "column": 37}, "end": {"line": 114, "column": 3}}}, "2": {"name": "(anonymous_4)", "decl": {"start": {"line": 116, "column": 10}, "end": {"line": 116, "column": 30}}, "loc": {"start": {"line": 116, "column": 59}, "end": {"line": 133, "column": 3}}}, "3": {"name": "(anonymous_5)", "decl": {"start": {"line": 135, "column": 10}, "end": {"line": 135, "column": 26}}, "loc": {"start": {"line": 135, "column": 42}, "end": {"line": 139, "column": 3}}}}, "branchMap": {"0": {"loc": {"start": {"line": 41, "column": 19}, "end": {"line": 41, "column": 121}}, "type": "binary-expr", "locations": [{"start": {"line": 41, "column": 19}, "end": {"line": 41, "column": 76}}, {"start": {"line": 41, "column": 80}, "end": {"line": 41, "column": 121}}]}, "1": {"loc": {"start": {"line": 43, "column": 4}, "end": {"line": 45, "column": 5}}, "type": "if", "locations": [{"start": {"line": 43, "column": 4}, "end": {"line": 45, "column": 5}}]}, "2": {"loc": {"start": {"line": 50, "column": 4}, "end": {"line": 52, "column": 5}}, "type": "if", "locations": [{"start": {"line": 50, "column": 4}, "end": {"line": 52, "column": 5}}]}, "3": {"loc": {"start": {"line": 50, "column": 8}, "end": {"line": 50, "column": 41}}, "type": "binary-expr", "locations": [{"start": {"line": 50, "column": 8}, "end": {"line": 50, "column": 13}}, {"start": {"line": 50, "column": 17}, "end": {"line": 50, "column": 41}}]}, "4": {"loc": {"start": {"line": 60, "column": 6}, "end": {"line": 63, "column": 7}}, "type": "if", "locations": [{"start": {"line": 60, "column": 6}, "end": {"line": 63, "column": 7}}]}, "5": {"loc": {"start": {"line": 69, "column": 4}, "end": {"line": 71, "column": 5}}, "type": "if", "locations": [{"start": {"line": 69, "column": 4}, "end": {"line": 71, "column": 5}}]}, "6": {"loc": {"start": {"line": 100, "column": 6}, "end": {"line": 110, "column": 7}}, "type": "if", "locations": [{"start": {"line": 100, "column": 6}, "end": {"line": 110, "column": 7}}]}, "7": {"loc": {"start": {"line": 100, "column": 10}, "end": {"line": 100, "column": 57}}, "type": "binary-expr", "locations": [{"start": {"line": 100, "column": 10}, "end": {"line": 100, "column": 28}}, {"start": {"line": 100, "column": 32}, "end": {"line": 100, "column": 57}}]}, "8": {"loc": {"start": {"line": 101, "column": 8}, "end": {"line": 103, "column": 9}}, "type": "if", "locations": [{"start": {"line": 101, "column": 8}, "end": {"line": 103, "column": 9}}]}, "9": {"loc": {"start": {"line": 104, "column": 8}, "end": {"line": 106, "column": 9}}, "type": "if", "locations": [{"start": {"line": 104, "column": 8}, "end": {"line": 106, "column": 9}}]}, "10": {"loc": {"start": {"line": 107, "column": 8}, "end": {"line": 109, "column": 9}}, "type": "if", "locations": [{"start": {"line": 107, "column": 8}, "end": {"line": 109, "column": 9}}]}, "11": {"loc": {"start": {"line": 130, "column": 30}, "end": {"line": 130, "column": 50}}, "type": "binary-expr", "locations": [{"start": {"line": 130, "column": 30}, "end": {"line": 130, "column": 45}}, {"start": {"line": 130, "column": 49}, "end": {"line": 130, "column": 50}}]}}, "s": {"0": 2, "1": 2, "2": 2, "3": 2, "4": 2, "5": 8, "6": 8, "7": 8, "8": 8, "9": 8, "10": 8, "11": 8, "12": 2, "13": 8, "14": 2, "15": 6, "16": 6, "17": 6, "18": 6, "19": 5, "20": 1, "21": 1, "22": 1, "23": 5, "24": 1, "25": 4, "26": 4, "27": 2, "28": 2, "29": 2, "30": 1, "31": 1, "32": 2, "33": 2, "34": 2, "35": 2, "36": 1, "37": 1, "38": 1, "39": 0, "40": 0, "41": 0, "42": 2, "43": 2, "44": 2, "45": 2, "46": 2, "47": 2, "48": 2}, "f": {"0": 8, "1": 8, "2": 2, "3": 2}, "b": {"0": [8, 1], "1": [2], "2": [2], "3": [8, 7], "4": [1], "5": [1], "6": [2], "7": [2, 0], "8": [1], "9": [1], "10": [0], "11": [2, 0]}}}