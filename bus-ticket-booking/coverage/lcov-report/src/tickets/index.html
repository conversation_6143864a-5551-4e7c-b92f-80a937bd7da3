
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/tickets</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> src/tickets</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">46.98% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>78/166</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">35.29% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>6/17</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">28.57% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>8/28</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">47.74% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>74/155</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="tickets.concurrency.spec.ts"><a href="tickets.concurrency.spec.ts.html">tickets.concurrency.spec.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="66" class="abs low">0/66</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="2" class="abs low">0/2</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="14" class="abs low">0/14</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="61" class="abs low">0/61</td>
	</tr>

<tr>
	<td class="file medium" data-value="tickets.controller.ts"><a href="tickets.controller.ts.html">tickets.controller.ts</a></td>
	<td data-value="78.26" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 78%"></div><div class="cover-empty" style="width: 22%"></div></div>
	</td>
	<td data-value="78.26" class="pct medium">78.26%</td>
	<td data-value="23" class="abs medium">18/23</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="16.66" class="pct low">16.66%</td>
	<td data-value="6" class="abs low">1/6</td>
	<td data-value="76.19" class="pct medium">76.19%</td>
	<td data-value="21" class="abs medium">16/21</td>
	</tr>

<tr>
	<td class="file low" data-value="tickets.module.ts"><a href="tickets.module.ts.html">tickets.module.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="8" class="abs low">0/8</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="6" class="abs low">0/6</td>
	</tr>

<tr>
	<td class="file high" data-value="tickets.service.ts"><a href="tickets.service.ts.html">tickets.service.ts</a></td>
	<td data-value="86.95" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 86%"></div><div class="cover-empty" style="width: 14%"></div></div>
	</td>
	<td data-value="86.95" class="pct high">86.95%</td>
	<td data-value="69" class="abs high">60/69</td>
	<td data-value="40" class="pct low">40%</td>
	<td data-value="15" class="abs low">6/15</td>
	<td data-value="87.5" class="pct high">87.5%</td>
	<td data-value="8" class="abs high">7/8</td>
	<td data-value="86.56" class="pct high">86.56%</td>
	<td data-value="67" class="abs high">58/67</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-03T08:40:15.810Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    