
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for src/trips</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="../../prettify.css" />
    <link rel="stylesheet" href="../../base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="../../favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(../../sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1><a href="../../index.html">All files</a> src/trips</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">63% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>63/100</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">18.18% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>4/22</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">42.85% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>6/14</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">62.36% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>58/93</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line medium'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file medium" data-value="trips.controller.ts"><a href="trips.controller.ts.html">trips.controller.ts</a></td>
	<td data-value="72" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 72%"></div><div class="cover-empty" style="width: 28%"></div></div>
	</td>
	<td data-value="72" class="pct medium">72%</td>
	<td data-value="25" class="abs medium">18/25</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="14.28" class="pct low">14.28%</td>
	<td data-value="7" class="abs low">1/7</td>
	<td data-value="69.56" class="pct medium">69.56%</td>
	<td data-value="23" class="abs medium">16/23</td>
	</tr>

<tr>
	<td class="file low" data-value="trips.module.ts"><a href="trips.module.ts.html">trips.module.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="8" class="abs low">0/8</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="6" class="abs low">0/6</td>
	</tr>

<tr>
	<td class="file medium" data-value="trips.service.ts"><a href="trips.service.ts.html">trips.service.ts</a></td>
	<td data-value="67.16" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 67%"></div><div class="cover-empty" style="width: 33%"></div></div>
	</td>
	<td data-value="67.16" class="pct medium">67.16%</td>
	<td data-value="67" class="abs medium">45/67</td>
	<td data-value="18.18" class="pct low">18.18%</td>
	<td data-value="22" class="abs low">4/22</td>
	<td data-value="71.42" class="pct medium">71.42%</td>
	<td data-value="7" class="abs medium">5/7</td>
	<td data-value="65.62" class="pct medium">65.62%</td>
	<td data-value="64" class="abs medium">42/64</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-03T08:40:15.810Z
            </div>
        <script src="../../prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="../../sorter.js"></script>
        <script src="../../block-navigation.js"></script>
    </body>
</html>
    