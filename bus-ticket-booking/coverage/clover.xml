<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1751532015831" clover="3.2.0">
  <project timestamp="1751532015831" name="All files">
    <metrics statements="951" coveredstatements="504" conditionals="177" coveredconditionals="33" methods="164" coveredmethods="47" elements="1292" coveredelements="584" complexity="0" loc="951" ncloc="951" packages="18" files="53" classes="53"/>
    <package name="src">
      <metrics statements="52" coveredstatements="9" conditionals="7" coveredconditionals="0" methods="7" coveredmethods="3"/>
      <file name="app.controller.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/app.controller.ts">
        <metrics statements="6" coveredstatements="6" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
        <line num="9" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
      </file>
      <file name="app.module.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/app.module.ts">
        <metrics statements="19" coveredstatements="0" conditionals="5" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="11" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="30" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
      </file>
      <file name="app.service.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/app.service.ts">
        <metrics statements="3" coveredstatements="3" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="1"/>
        <line num="1" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="6" count="1" type="stmt"/>
      </file>
      <file name="main.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/main.ts">
        <metrics statements="24" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="60" count="0" type="stmt"/>
        <line num="61" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="72" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.admin">
      <metrics statements="6" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="admin.module.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/admin/admin.module.ts">
        <metrics statements="6" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.admin.drivers">
      <metrics statements="51" coveredstatements="22" conditionals="9" coveredconditionals="0" methods="12" coveredmethods="2"/>
      <file name="drivers.controller.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/admin/drivers/drivers.controller.ts">
        <metrics statements="21" coveredstatements="15" conditionals="0" coveredconditionals="0" methods="6" coveredmethods="1"/>
        <line num="1" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="44" count="1" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="63" count="1" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="73" count="1" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
      </file>
      <file name="drivers.service.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/admin/drivers/drivers.service.ts">
        <metrics statements="30" coveredstatements="7" conditionals="9" coveredconditionals="0" methods="6" coveredmethods="1"/>
        <line num="1" count="2" type="stmt"/>
        <line num="2" count="2" type="stmt"/>
        <line num="3" count="2" type="stmt"/>
        <line num="4" count="2" type="stmt"/>
        <line num="8" count="2" type="stmt"/>
        <line num="11" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="22" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="23" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="47" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="48" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="59" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="64" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="76" count="0" type="stmt"/>
        <line num="80" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="81" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.auth">
      <metrics statements="52" coveredstatements="41" conditionals="3" coveredconditionals="3" methods="8" coveredmethods="7"/>
      <file name="auth.controller.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/auth/auth.controller.ts">
        <metrics statements="10" coveredstatements="10" conditionals="0" coveredconditionals="0" methods="3" coveredmethods="3"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="8" count="1" type="stmt"/>
        <line num="9" count="2" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
      </file>
      <file name="auth.module.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/auth/auth.module.ts">
        <metrics statements="11" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
      </file>
      <file name="auth.service.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/auth/auth.service.ts">
        <metrics statements="31" coveredstatements="31" conditionals="3" coveredconditionals="3" methods="4" coveredmethods="4"/>
        <line num="1" count="3" type="stmt"/>
        <line num="2" count="3" type="stmt"/>
        <line num="3" count="3" type="stmt"/>
        <line num="4" count="3" type="stmt"/>
        <line num="5" count="3" type="stmt"/>
        <line num="6" count="3" type="stmt"/>
        <line num="10" count="3" type="stmt"/>
        <line num="13" count="7" type="stmt"/>
        <line num="14" count="7" type="stmt"/>
        <line num="18" count="2" type="stmt"/>
        <line num="21" count="2" type="stmt"/>
        <line num="22" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="23" count="1" type="stmt"/>
        <line num="27" count="1" type="stmt"/>
        <line num="28" count="1" type="stmt"/>
        <line num="31" count="1" type="stmt"/>
        <line num="39" count="1" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="43" count="1" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="58" count="3" type="stmt"/>
        <line num="61" count="3" type="stmt"/>
        <line num="62" count="3" type="cond" truecount="1" falsecount="0"/>
        <line num="63" count="1" type="stmt"/>
        <line num="67" count="2" type="stmt"/>
        <line num="68" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="69" count="1" type="stmt"/>
        <line num="73" count="1" type="stmt"/>
        <line num="74" count="1" type="stmt"/>
        <line num="76" count="1" type="stmt"/>
        <line num="89" count="2" type="stmt"/>
      </file>
    </package>
    <package name="src.auth.decorators">
      <metrics statements="8" coveredstatements="4" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="1"/>
      <file name="current-user.decorator.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/auth/decorators/current-user.decorator.ts">
        <metrics statements="4" coveredstatements="2" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/auth/decorators/index.ts">
        <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
      </file>
      <file name="roles.decorator.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/auth/decorators/roles.decorator.ts">
        <metrics statements="2" coveredstatements="2" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="1"/>
        <line num="1" count="3" type="stmt"/>
        <line num="4" count="6" type="stmt"/>
      </file>
    </package>
    <package name="src.auth.guards">
      <metrics statements="12" coveredstatements="12" conditionals="1" coveredconditionals="1" methods="3" coveredmethods="3"/>
      <file name="jwt-auth.guard.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/auth/guards/jwt-auth.guard.ts">
        <metrics statements="3" coveredstatements="3" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="3" type="stmt"/>
        <line num="2" count="3" type="stmt"/>
        <line num="5" count="3" type="stmt"/>
      </file>
      <file name="roles.guard.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/auth/guards/roles.guard.ts">
        <metrics statements="9" coveredstatements="9" conditionals="1" coveredconditionals="1" methods="3" coveredmethods="3"/>
        <line num="1" count="4" type="stmt"/>
        <line num="2" count="4" type="stmt"/>
        <line num="6" count="4" type="stmt"/>
        <line num="7" count="8" type="stmt"/>
        <line num="10" count="5" type="stmt"/>
        <line num="15" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="16" count="1" type="stmt"/>
        <line num="19" count="4" type="stmt"/>
        <line num="20" count="5" type="stmt"/>
      </file>
    </package>
    <package name="src.auth.strategies">
      <metrics statements="13" coveredstatements="13" conditionals="3" coveredconditionals="2" methods="2" coveredmethods="2"/>
      <file name="jwt.strategy.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/auth/strategies/jwt.strategy.ts">
        <metrics statements="13" coveredstatements="13" conditionals="3" coveredconditionals="2" methods="2" coveredmethods="2"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="18" count="2" type="stmt"/>
        <line num="19" count="2" type="stmt"/>
        <line num="21" count="2" type="stmt"/>
        <line num="29" count="2" type="stmt"/>
        <line num="30" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="31" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.common">
      <metrics statements="11" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      <file name="common.module.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/common/common.module.ts">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/common/index.ts">
        <metrics statements="7" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.common.filters">
      <metrics statements="30" coveredstatements="0" conditionals="11" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="all-exceptions.filter.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/common/filters/all-exceptions.filter.ts">
        <metrics statements="16" coveredstatements="0" conditionals="5" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="23" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="27" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
      </file>
      <file name="http-exception.filter.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/common/filters/http-exception.filter.ts">
        <metrics statements="14" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="16" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="21" count="0" type="stmt"/>
        <line num="30" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="33" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.common.guards">
      <metrics statements="21" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="4" coveredmethods="0"/>
      <file name="redis-throttler.guard.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/common/guards/redis-throttler.guard.ts">
        <metrics statements="21" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="20" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="37" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="43" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="45" count="0" type="stmt"/>
        <line num="48" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.common.interceptors">
      <metrics statements="24" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="4" coveredmethods="0"/>
      <file name="logging.interceptor.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/common/interceptors/logging.interceptor.ts">
        <metrics statements="16" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="20" count="0" type="stmt"/>
        <line num="21" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="23" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="31" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
      </file>
      <file name="transform.interceptor.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/common/interceptors/transform.interceptor.ts">
        <metrics statements="8" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="19" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="27" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.common.services">
      <metrics statements="113" coveredstatements="4" conditionals="24" coveredconditionals="0" methods="28" coveredmethods="0"/>
      <file name="concurrency.service.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/common/services/concurrency.service.ts">
        <metrics statements="54" coveredstatements="0" conditionals="19" coveredconditionals="0" methods="14" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="42" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="46" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="47" count="0" type="stmt"/>
        <line num="52" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="57" count="0" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="95" count="0" type="stmt"/>
        <line num="96" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="97" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="155" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="156" count="0" type="stmt"/>
        <line num="157" count="0" type="stmt"/>
        <line num="161" count="0" type="stmt"/>
        <line num="162" count="0" type="stmt"/>
        <line num="164" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="189" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
        <line num="194" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="196" count="0" type="stmt"/>
        <line num="199" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="200" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="207" count="0" type="stmt"/>
        <line num="209" count="0" type="stmt"/>
        <line num="210" count="0" type="stmt"/>
        <line num="211" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
      </file>
      <file name="redis.service.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/common/services/redis.service.ts">
        <metrics statements="59" coveredstatements="4" conditionals="5" coveredconditionals="0" methods="14" coveredmethods="0"/>
        <line num="1" count="2" type="stmt"/>
        <line num="2" count="2" type="stmt"/>
        <line num="3" count="2" type="stmt"/>
        <line num="6" count="2" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="15" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="37" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="44" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="45" count="0" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="53" count="0" type="stmt"/>
        <line num="54" count="0" type="stmt"/>
        <line num="59" count="0" type="stmt"/>
        <line num="60" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="61" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="67" count="0" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="73" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="77" count="0" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="85" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="106" count="0" type="stmt"/>
        <line num="107" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="113" count="0" type="stmt"/>
        <line num="114" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="116" count="0" type="stmt"/>
        <line num="117" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="127" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.config">
      <metrics statements="16" coveredstatements="0" conditionals="52" coveredconditionals="0" methods="6" coveredmethods="0"/>
      <file name="app.config.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/config/app.config.ts">
        <metrics statements="2" coveredstatements="0" conditionals="24" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
      </file>
      <file name="database.config.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/config/database.config.ts">
        <metrics statements="4" coveredstatements="0" conditionals="12" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/config/index.ts">
        <metrics statements="3" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="3" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
      </file>
      <file name="redis.config.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/config/redis.config.ts">
        <metrics statements="2" coveredstatements="0" conditionals="6" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
      </file>
      <file name="typeorm.config.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/config/typeorm.config.ts">
        <metrics statements="5" coveredstatements="0" conditionals="10" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.dto">
      <metrics statements="133" coveredstatements="131" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="auth.dto.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/dto/auth.dto.ts">
        <metrics statements="13" coveredstatements="13" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="5" type="stmt"/>
        <line num="2" count="5" type="stmt"/>
        <line num="4" count="5" type="stmt"/>
        <line num="7" count="5" type="stmt"/>
        <line num="12" count="5" type="stmt"/>
        <line num="15" count="5" type="stmt"/>
        <line num="18" count="5" type="stmt"/>
        <line num="23" count="5" type="stmt"/>
        <line num="27" count="5" type="stmt"/>
        <line num="31" count="5" type="stmt"/>
        <line num="34" count="5" type="stmt"/>
        <line num="36" count="5" type="stmt"/>
        <line num="39" count="5" type="stmt"/>
      </file>
      <file name="driver.dto.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/dto/driver.dto.ts">
        <metrics statements="17" coveredstatements="17" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="5" type="stmt"/>
        <line num="2" count="5" type="stmt"/>
        <line num="4" count="5" type="stmt"/>
        <line num="8" count="5" type="stmt"/>
        <line num="13" count="5" type="stmt"/>
        <line num="18" count="5" type="stmt"/>
        <line num="21" count="5" type="stmt"/>
        <line num="26" count="5" type="stmt"/>
        <line num="32" count="5" type="stmt"/>
        <line num="38" count="5" type="stmt"/>
        <line num="41" count="5" type="stmt"/>
        <line num="43" count="5" type="stmt"/>
        <line num="46" count="5" type="stmt"/>
        <line num="49" count="5" type="stmt"/>
        <line num="52" count="5" type="stmt"/>
        <line num="55" count="5" type="stmt"/>
        <line num="58" count="5" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/dto/index.ts">
        <metrics statements="6" coveredstatements="6" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="5" type="stmt"/>
        <line num="2" count="5" type="stmt"/>
        <line num="3" count="5" type="stmt"/>
        <line num="4" count="5" type="stmt"/>
        <line num="5" count="5" type="stmt"/>
        <line num="6" count="5" type="stmt"/>
      </file>
      <file name="ticket.dto.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/dto/ticket.dto.ts">
        <metrics statements="19" coveredstatements="19" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="5" type="stmt"/>
        <line num="2" count="5" type="stmt"/>
        <line num="3" count="5" type="stmt"/>
        <line num="5" count="5" type="stmt"/>
        <line num="8" count="5" type="stmt"/>
        <line num="12" count="5" type="stmt"/>
        <line num="15" count="5" type="stmt"/>
        <line num="19" count="5" type="stmt"/>
        <line num="22" count="5" type="stmt"/>
        <line num="24" count="5" type="stmt"/>
        <line num="27" count="5" type="stmt"/>
        <line num="30" count="5" type="stmt"/>
        <line num="33" count="5" type="stmt"/>
        <line num="36" count="5" type="stmt"/>
        <line num="39" count="5" type="stmt"/>
        <line num="42" count="5" type="stmt"/>
        <line num="45" count="5" type="stmt"/>
        <line num="48" count="5" type="stmt"/>
        <line num="58" count="5" type="stmt"/>
      </file>
      <file name="trip.dto.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/dto/trip.dto.ts">
        <metrics statements="38" coveredstatements="36" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="1" count="5" type="stmt"/>
        <line num="2" count="5" type="stmt"/>
        <line num="3" count="5" type="stmt"/>
        <line num="5" count="5" type="stmt"/>
        <line num="8" count="5" type="stmt"/>
        <line num="12" count="5" type="stmt"/>
        <line num="16" count="5" type="stmt"/>
        <line num="20" count="5" type="stmt"/>
        <line num="25" count="5" type="stmt"/>
        <line num="31" count="5" type="stmt"/>
        <line num="35" count="5" type="stmt"/>
        <line num="38" count="5" type="stmt"/>
        <line num="42" count="5" type="stmt"/>
        <line num="47" count="5" type="stmt"/>
        <line num="52" count="5" type="stmt"/>
        <line num="57" count="5" type="stmt"/>
        <line num="63" count="5" type="stmt"/>
        <line num="68" count="5" type="stmt"/>
        <line num="71" count="5" type="stmt"/>
        <line num="74" count="5" type="stmt"/>
        <line num="78" count="5" type="stmt"/>
        <line num="82" count="5" type="stmt"/>
        <line num="86" count="0" type="stmt"/>
        <line num="89" count="5" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="97" count="5" type="stmt"/>
        <line num="100" count="5" type="stmt"/>
        <line num="102" count="5" type="stmt"/>
        <line num="105" count="5" type="stmt"/>
        <line num="108" count="5" type="stmt"/>
        <line num="111" count="5" type="stmt"/>
        <line num="114" count="5" type="stmt"/>
        <line num="117" count="5" type="stmt"/>
        <line num="120" count="5" type="stmt"/>
        <line num="123" count="5" type="stmt"/>
        <line num="126" count="5" type="stmt"/>
        <line num="129" count="5" type="stmt"/>
        <line num="132" count="5" type="stmt"/>
      </file>
      <file name="user.dto.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/dto/user.dto.ts">
        <metrics statements="22" coveredstatements="22" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="5" type="stmt"/>
        <line num="2" count="5" type="stmt"/>
        <line num="3" count="5" type="stmt"/>
        <line num="5" count="5" type="stmt"/>
        <line num="8" count="5" type="stmt"/>
        <line num="13" count="5" type="stmt"/>
        <line num="18" count="5" type="stmt"/>
        <line num="23" count="5" type="stmt"/>
        <line num="28" count="5" type="stmt"/>
        <line num="31" count="5" type="stmt"/>
        <line num="35" count="5" type="stmt"/>
        <line num="41" count="5" type="stmt"/>
        <line num="47" count="5" type="stmt"/>
        <line num="52" count="5" type="stmt"/>
        <line num="55" count="5" type="stmt"/>
        <line num="57" count="5" type="stmt"/>
        <line num="60" count="5" type="stmt"/>
        <line num="63" count="5" type="stmt"/>
        <line num="66" count="5" type="stmt"/>
        <line num="69" count="5" type="stmt"/>
        <line num="72" count="5" type="stmt"/>
        <line num="75" count="5" type="stmt"/>
      </file>
      <file name="weather.dto.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/dto/weather.dto.ts">
        <metrics statements="18" coveredstatements="18" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="5" type="stmt"/>
        <line num="2" count="5" type="stmt"/>
        <line num="4" count="5" type="stmt"/>
        <line num="14" count="5" type="stmt"/>
        <line num="17" count="5" type="stmt"/>
        <line num="19" count="5" type="stmt"/>
        <line num="22" count="5" type="stmt"/>
        <line num="25" count="5" type="stmt"/>
        <line num="28" count="5" type="stmt"/>
        <line num="31" count="5" type="stmt"/>
        <line num="34" count="5" type="stmt"/>
        <line num="37" count="5" type="stmt"/>
        <line num="40" count="5" type="stmt"/>
        <line num="43" count="5" type="stmt"/>
        <line num="46" count="5" type="stmt"/>
        <line num="49" count="5" type="stmt"/>
        <line num="52" count="5" type="stmt"/>
        <line num="55" count="5" type="stmt"/>
      </file>
    </package>
    <package name="src.entities">
      <metrics statements="100" coveredstatements="83" conditionals="4" coveredconditionals="4" methods="36" coveredmethods="9"/>
      <file name="driver.entity.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/entities/driver.entity.ts">
        <metrics statements="11" coveredstatements="10" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="1" count="11" type="stmt"/>
        <line num="10" count="11" type="stmt"/>
        <line num="14" count="11" type="stmt"/>
        <line num="16" count="11" type="stmt"/>
        <line num="19" count="11" type="stmt"/>
        <line num="22" count="11" type="stmt"/>
        <line num="25" count="11" type="stmt"/>
        <line num="28" count="11" type="stmt"/>
        <line num="31" count="11" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="35" count="11" type="stmt"/>
      </file>
      <file name="index.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/entities/index.ts">
        <metrics statements="5" coveredstatements="5" conditionals="0" coveredconditionals="0" methods="7" coveredmethods="7"/>
        <line num="1" count="65" type="stmt"/>
        <line num="2" count="24" type="stmt"/>
        <line num="3" count="38" type="stmt"/>
        <line num="4" count="37" type="stmt"/>
        <line num="5" count="35" type="stmt"/>
      </file>
      <file name="seat.entity.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/entities/seat.entity.ts">
        <metrics statements="15" coveredstatements="13" conditionals="0" coveredconditionals="0" methods="4" coveredmethods="0"/>
        <line num="1" count="11" type="stmt"/>
        <line num="14" count="11" type="stmt"/>
        <line num="15" count="11" type="stmt"/>
        <line num="20" count="11" type="stmt"/>
        <line num="22" count="11" type="stmt"/>
        <line num="25" count="11" type="stmt"/>
        <line num="28" count="11" type="stmt"/>
        <line num="31" count="11" type="stmt"/>
        <line num="34" count="11" type="stmt"/>
        <line num="37" count="11" type="stmt"/>
        <line num="40" count="11" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="48" count="11" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="51" count="11" type="stmt"/>
      </file>
      <file name="ticket.entity.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/entities/ticket.entity.ts">
        <metrics statements="25" coveredstatements="19" conditionals="2" coveredconditionals="2" methods="10" coveredmethods="1"/>
        <line num="1" count="11" type="stmt"/>
        <line num="12" count="11" type="stmt"/>
        <line num="13" count="11" type="stmt"/>
        <line num="14" count="11" type="stmt"/>
        <line num="16" count="11" type="cond" truecount="2" falsecount="0"/>
        <line num="17" count="11" type="stmt"/>
        <line num="18" count="11" type="stmt"/>
        <line num="25" count="11" type="stmt"/>
        <line num="27" count="11" type="stmt"/>
        <line num="30" count="11" type="stmt"/>
        <line num="33" count="11" type="stmt"/>
        <line num="36" count="11" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="11" type="stmt"/>
        <line num="46" count="11" type="stmt"/>
        <line num="49" count="11" type="stmt"/>
        <line num="52" count="11" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="60" count="11" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="67" count="11" type="stmt"/>
        <line num="69" count="0" type="stmt"/>
        <line num="74" count="11" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
      </file>
      <file name="trip.entity.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/entities/trip.entity.ts">
        <metrics statements="26" coveredstatements="20" conditionals="0" coveredconditionals="0" methods="9" coveredmethods="0"/>
        <line num="1" count="11" type="stmt"/>
        <line num="14" count="11" type="stmt"/>
        <line num="15" count="11" type="stmt"/>
        <line num="16" count="11" type="stmt"/>
        <line num="25" count="11" type="stmt"/>
        <line num="27" count="11" type="stmt"/>
        <line num="30" count="11" type="stmt"/>
        <line num="33" count="11" type="stmt"/>
        <line num="36" count="11" type="stmt"/>
        <line num="39" count="11" type="stmt"/>
        <line num="42" count="11" type="stmt"/>
        <line num="45" count="11" type="stmt"/>
        <line num="48" count="11" type="stmt"/>
        <line num="51" count="11" type="stmt"/>
        <line num="54" count="11" type="stmt"/>
        <line num="57" count="11" type="stmt"/>
        <line num="60" count="11" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="68" count="11" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
        <line num="73" count="11" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="76" count="11" type="stmt"/>
        <line num="80" count="0" type="stmt"/>
        <line num="84" count="0" type="stmt"/>
        <line num="88" count="0" type="stmt"/>
      </file>
      <file name="user.entity.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/entities/user.entity.ts">
        <metrics statements="18" coveredstatements="16" conditionals="2" coveredconditionals="2" methods="4" coveredmethods="1"/>
        <line num="1" count="11" type="stmt"/>
        <line num="10" count="11" type="stmt"/>
        <line num="11" count="11" type="stmt"/>
        <line num="13" count="11" type="cond" truecount="2" falsecount="0"/>
        <line num="14" count="11" type="stmt"/>
        <line num="15" count="11" type="stmt"/>
        <line num="20" count="11" type="stmt"/>
        <line num="22" count="11" type="stmt"/>
        <line num="25" count="11" type="stmt"/>
        <line num="29" count="11" type="stmt"/>
        <line num="32" count="11" type="stmt"/>
        <line num="35" count="11" type="stmt"/>
        <line num="42" count="11" type="stmt"/>
        <line num="45" count="11" type="stmt"/>
        <line num="48" count="11" type="stmt"/>
        <line num="51" count="0" type="stmt"/>
        <line num="52" count="11" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.tickets">
      <metrics statements="155" coveredstatements="74" conditionals="17" coveredconditionals="6" methods="28" coveredmethods="8"/>
      <file name="tickets.concurrency.spec.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/tickets/tickets.concurrency.spec.ts">
        <metrics statements="61" coveredstatements="0" conditionals="2" coveredconditionals="0" methods="14" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="6" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="32" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="36" count="0" type="stmt"/>
        <line num="38" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="41" count="0" type="stmt"/>
        <line num="44" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="47" count="0" type="stmt"/>
        <line num="55" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="81" count="0" type="stmt"/>
        <line num="87" count="0" type="stmt"/>
        <line num="89" count="0" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="92" count="0" type="stmt"/>
        <line num="93" count="0" type="stmt"/>
        <line num="96" count="0" type="stmt"/>
        <line num="97" count="0" type="stmt"/>
        <line num="100" count="0" type="stmt"/>
        <line num="101" count="0" type="stmt"/>
        <line num="104" count="0" type="stmt"/>
        <line num="105" count="0" type="stmt"/>
        <line num="108" count="0" type="stmt"/>
        <line num="110" count="0" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="147" count="0" type="stmt"/>
        <line num="166" count="0" type="stmt"/>
        <line num="167" count="0" type="stmt"/>
        <line num="171" count="0" type="stmt"/>
        <line num="173" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="stmt"/>
        <line num="181" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="195" count="0" type="stmt"/>
        <line num="206" count="0" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="214" count="0" type="stmt"/>
        <line num="217" count="0" type="stmt"/>
        <line num="220" count="0" type="stmt"/>
        <line num="221" count="0" type="cond" truecount="0" falsecount="2"/>
      </file>
      <file name="tickets.controller.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/tickets/tickets.controller.ts">
        <metrics statements="21" coveredstatements="16" conditionals="0" coveredconditionals="0" methods="6" coveredmethods="1"/>
        <line num="1" count="1" type="stmt"/>
        <line num="12" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="25" count="1" type="stmt"/>
        <line num="26" count="1" type="stmt"/>
        <line num="33" count="1" type="stmt"/>
        <line num="34" count="0" type="stmt"/>
        <line num="42" count="1" type="stmt"/>
        <line num="43" count="0" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="50" count="0" type="stmt"/>
        <line num="61" count="1" type="stmt"/>
        <line num="62" count="0" type="stmt"/>
        <line num="69" count="1" type="stmt"/>
        <line num="70" count="0" type="stmt"/>
      </file>
      <file name="tickets.module.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/tickets/tickets.module.ts">
        <metrics statements="6" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
      </file>
      <file name="tickets.service.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/tickets/tickets.service.ts">
        <metrics statements="67" coveredstatements="58" conditionals="15" coveredconditionals="6" methods="8" coveredmethods="7"/>
        <line num="1" count="2" type="stmt"/>
        <line num="2" count="2" type="stmt"/>
        <line num="3" count="2" type="stmt"/>
        <line num="4" count="2" type="stmt"/>
        <line num="8" count="2" type="stmt"/>
        <line num="11" count="8" type="stmt"/>
        <line num="13" count="8" type="stmt"/>
        <line num="15" count="8" type="stmt"/>
        <line num="17" count="8" type="stmt"/>
        <line num="18" count="8" type="stmt"/>
        <line num="22" count="3" type="stmt"/>
        <line num="24" count="3" type="stmt"/>
        <line num="26" count="3" type="stmt"/>
        <line num="31" count="3" type="cond" truecount="1" falsecount="0"/>
        <line num="32" count="1" type="stmt"/>
        <line num="35" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="36" count="1" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="45" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="46" count="0" type="stmt"/>
        <line num="49" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="50" count="0" type="stmt"/>
        <line num="54" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="55" count="0" type="stmt"/>
        <line num="59" count="1" type="stmt"/>
        <line num="60" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="61" count="0" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="69" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="70" count="0" type="stmt"/>
        <line num="74" count="1" type="stmt"/>
        <line num="82" count="1" type="stmt"/>
        <line num="85" count="1" type="stmt"/>
        <line num="86" count="1" type="stmt"/>
        <line num="89" count="1" type="stmt"/>
        <line num="90" count="1" type="stmt"/>
        <line num="92" count="1" type="stmt"/>
        <line num="97" count="2" type="stmt"/>
        <line num="99" count="2" type="stmt"/>
        <line num="105" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="106" count="0" type="stmt"/>
        <line num="109" count="2" type="cond" truecount="0" falsecount="1"/>
        <line num="110" count="0" type="stmt"/>
        <line num="114" count="2" type="stmt"/>
        <line num="115" count="2" type="stmt"/>
        <line num="116" count="2" type="stmt"/>
        <line num="117" count="2" type="stmt"/>
        <line num="119" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="120" count="1" type="stmt"/>
        <line num="124" count="1" type="stmt"/>
        <line num="125" count="1" type="stmt"/>
        <line num="128" count="1" type="stmt"/>
        <line num="133" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="134" count="1" type="stmt"/>
        <line num="135" count="1" type="stmt"/>
        <line num="139" count="1" type="stmt"/>
        <line num="144" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="145" count="1" type="stmt"/>
        <line num="146" count="1" type="stmt"/>
        <line num="149" count="1" type="stmt"/>
        <line num="154" count="1" type="stmt"/>
        <line num="162" count="2" type="stmt"/>
        <line num="167" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="168" count="1" type="stmt"/>
        <line num="171" count="1" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.trips">
      <metrics statements="93" coveredstatements="58" conditionals="22" coveredconditionals="4" methods="14" coveredmethods="6"/>
      <file name="trips.controller.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/trips/trips.controller.ts">
        <metrics statements="23" coveredstatements="16" conditionals="0" coveredconditionals="0" methods="7" coveredmethods="1"/>
        <line num="1" count="1" type="stmt"/>
        <line num="13" count="1" type="stmt"/>
        <line num="14" count="1" type="stmt"/>
        <line num="15" count="1" type="stmt"/>
        <line num="16" count="1" type="stmt"/>
        <line num="17" count="1" type="stmt"/>
        <line num="18" count="1" type="stmt"/>
        <line num="19" count="1" type="stmt"/>
        <line num="23" count="1" type="stmt"/>
        <line num="24" count="1" type="stmt"/>
        <line num="34" count="1" type="stmt"/>
        <line num="35" count="0" type="stmt"/>
        <line num="45" count="1" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="57" count="1" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="77" count="1" type="stmt"/>
        <line num="78" count="0" type="stmt"/>
        <line num="89" count="1" type="stmt"/>
        <line num="90" count="0" type="stmt"/>
        <line num="91" count="0" type="stmt"/>
      </file>
      <file name="trips.module.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/trips/trips.module.ts">
        <metrics statements="6" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
      </file>
      <file name="trips.service.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/trips/trips.service.ts">
        <metrics statements="64" coveredstatements="42" conditionals="22" coveredconditionals="4" methods="7" coveredmethods="5"/>
        <line num="1" count="2" type="stmt"/>
        <line num="2" count="2" type="stmt"/>
        <line num="3" count="2" type="stmt"/>
        <line num="4" count="2" type="stmt"/>
        <line num="8" count="2" type="stmt"/>
        <line num="11" count="8" type="stmt"/>
        <line num="13" count="8" type="stmt"/>
        <line num="15" count="8" type="stmt"/>
        <line num="19" count="3" type="stmt"/>
        <line num="22" count="3" type="stmt"/>
        <line num="23" count="3" type="cond" truecount="1" falsecount="0"/>
        <line num="24" count="1" type="stmt"/>
        <line num="28" count="2" type="stmt"/>
        <line num="29" count="2" type="stmt"/>
        <line num="30" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="31" count="1" type="stmt"/>
        <line num="35" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="36" count="0" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="49" count="1" type="stmt"/>
        <line num="52" count="1" type="stmt"/>
        <line num="53" count="1" type="stmt"/>
        <line num="54" count="40" type="stmt"/>
        <line num="59" count="40" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
        <line num="64" count="1" type="stmt"/>
        <line num="68" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="79" count="5" type="stmt"/>
        <line num="84" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="85" count="1" type="stmt"/>
        <line num="88" count="4" type="stmt"/>
        <line num="92" count="1" type="cond" truecount="0" falsecount="2"/>
        <line num="95" count="1" type="stmt"/>
        <line num="96" count="1" type="stmt"/>
        <line num="97" count="1" type="stmt"/>
        <line num="98" count="1" type="stmt"/>
        <line num="99" count="1" type="stmt"/>
        <line num="101" count="1" type="stmt"/>
        <line num="114" count="1" type="stmt"/>
        <line num="118" count="0" type="stmt"/>
        <line num="121" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="122" count="0" type="stmt"/>
        <line num="123" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="124" count="0" type="stmt"/>
        <line num="129" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="130" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="131" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="133" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="134" count="0" type="stmt"/>
        <line num="137" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="138" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="145" count="0" type="stmt"/>
        <line num="147" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="148" count="0" type="stmt"/>
        <line num="151" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="156" count="2" type="stmt"/>
        <line num="159" count="2" type="stmt"/>
        <line num="163" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="164" count="1" type="stmt"/>
        <line num="167" count="1" type="stmt"/>
      </file>
    </package>
    <package name="src.weather">
      <metrics statements="61" coveredstatements="53" conditionals="16" coveredconditionals="13" methods="6" coveredmethods="6"/>
      <file name="weather.controller.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/weather/weather.controller.ts">
        <metrics statements="9" coveredstatements="9" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="2"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="5" count="1" type="stmt"/>
        <line num="10" count="1" type="stmt"/>
        <line num="11" count="2" type="stmt"/>
        <line num="40" count="1" type="stmt"/>
        <line num="41" count="2" type="stmt"/>
      </file>
      <file name="weather.module.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/weather/weather.module.ts">
        <metrics statements="5" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="12" count="0" type="stmt"/>
      </file>
      <file name="weather.service.ts" path="/Users/<USER>/privatespace/untitled folder/bus-ticket-booking/src/weather/weather.service.ts">
        <metrics statements="47" coveredstatements="44" conditionals="16" coveredconditionals="13" methods="4" coveredmethods="4"/>
        <line num="1" count="2" type="stmt"/>
        <line num="2" count="2" type="stmt"/>
        <line num="3" count="2" type="stmt"/>
        <line num="4" count="2" type="stmt"/>
        <line num="30" count="2" type="stmt"/>
        <line num="31" count="8" type="stmt"/>
        <line num="34" count="8" type="stmt"/>
        <line num="37" count="8" type="stmt"/>
        <line num="38" count="8" type="stmt"/>
        <line num="40" count="8" type="stmt"/>
        <line num="41" count="8" type="cond" truecount="2" falsecount="0"/>
        <line num="43" count="8" type="cond" truecount="1" falsecount="0"/>
        <line num="44" count="2" type="stmt"/>
        <line num="50" count="8" type="cond" truecount="3" falsecount="0"/>
        <line num="51" count="2" type="stmt"/>
        <line num="54" count="6" type="stmt"/>
        <line num="55" count="6" type="stmt"/>
        <line num="58" count="6" type="stmt"/>
        <line num="59" count="6" type="stmt"/>
        <line num="60" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="61" count="1" type="stmt"/>
        <line num="62" count="1" type="stmt"/>
        <line num="65" count="1" type="stmt"/>
        <line num="69" count="5" type="cond" truecount="1" falsecount="0"/>
        <line num="70" count="1" type="stmt"/>
        <line num="73" count="4" type="stmt"/>
        <line num="74" count="4" type="stmt"/>
        <line num="86" count="2" type="stmt"/>
        <line num="89" count="2" type="stmt"/>
        <line num="90" count="2" type="stmt"/>
        <line num="91" count="1" type="stmt"/>
        <line num="93" count="1" type="stmt"/>
        <line num="96" count="2" type="stmt"/>
        <line num="98" count="2" type="stmt"/>
        <line num="100" count="2" type="cond" truecount="2" falsecount="1"/>
        <line num="101" count="2" type="cond" truecount="1" falsecount="0"/>
        <line num="102" count="1" type="stmt"/>
        <line num="104" count="1" type="cond" truecount="1" falsecount="0"/>
        <line num="105" count="1" type="stmt"/>
        <line num="107" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="108" count="0" type="stmt"/>
        <line num="112" count="0" type="stmt"/>
        <line num="117" count="2" type="stmt"/>
        <line num="119" count="2" type="stmt"/>
        <line num="136" count="2" type="stmt"/>
        <line num="137" count="2" type="stmt"/>
        <line num="138" count="2" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
