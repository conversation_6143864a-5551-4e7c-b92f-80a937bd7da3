import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { writeFileSync, mkdirSync } from 'fs';
import { join } from 'path';
import { AppModule } from '../src/app.module';

async function generateApiDocs() {
  const app = await NestFactory.create(AppModule, { logger: false });

  const config = new DocumentBuilder()
    .setTitle('Bus Ticket Booking API')
    .setDescription(`
# Bus Ticket Booking System API

A comprehensive REST API for managing bus ticket bookings with the following features:

## Features
- **User Authentication**: JWT-based authentication with role-based access control
- **Trip Management**: Search and manage bus trips with real-time availability
- **Ticket Booking**: Secure booking system with concurrency handling
- **Weather Integration**: Get weather information for travel destinations
- **Admin Panel**: Administrative functions for managing drivers and trips

## Authentication
Most endpoints require authentication using JWT tokens. Include the token in the Authorization header:
\`\`\`
Authorization: Bearer <your-jwt-token>
\`\`\`

## Rate Limiting
API endpoints are rate-limited to prevent abuse. Default limits:
- 100 requests per minute for authenticated users
- 20 requests per minute for unauthenticated users

## Error Handling
The API uses standard HTTP status codes and returns errors in the following format:
\`\`\`json
{
  "statusCode": 400,
  "timestamp": "2024-01-15T10:30:00.000Z",
  "path": "/api/endpoint",
  "method": "POST",
  "message": "Error description"
}
\`\`\`

## Pagination
List endpoints support pagination with the following query parameters:
- \`page\`: Page number (default: 1)
- \`limit\`: Items per page (default: 10, max: 100)

## Concurrency
The booking system handles concurrent requests safely using:
- Database transactions
- Pessimistic locking for seat reservations
- Optimistic locking with version control
    `)
    .setVersion('1.0.0')
    .addBearerAuth()
    .addServer('http://localhost:3000', 'Development server')
    .addServer('https://api.bus-booking.com', 'Production server')
    .addTag('Authentication', 'User authentication and authorization')
    .addTag('Trips', 'Trip management and search')
    .addTag('Tickets', 'Ticket booking and management')
    .addTag('Weather', 'Weather information for cities')
    .addTag('Admin - Drivers', 'Driver management (Admin only)')
    .setContact('Bus Booking API Support', 'https://bus-booking.com/support', '<EMAIL>')
    .setLicense('MIT', 'https://opensource.org/licenses/MIT')
    .build();

  const document = SwaggerModule.createDocument(app, config);

  // Create docs directory if it doesn't exist
  const docsDir = join(process.cwd(), 'docs');
  try {
    mkdirSync(docsDir, { recursive: true });
  } catch (error) {
    // Directory might already exist
  }

  // Generate OpenAPI JSON
  const openApiJson = JSON.stringify(document, null, 2);
  writeFileSync(join(docsDir, 'openapi.json'), openApiJson);

  // Generate OpenAPI YAML
  const yaml = require('js-yaml');
  const openApiYaml = yaml.dump(document);
  writeFileSync(join(docsDir, 'openapi.yaml'), openApiYaml);

  // Generate HTML documentation
  const htmlTemplate = `
<!DOCTYPE html>
<html>
<head>
  <title>Bus Ticket Booking API Documentation</title>
  <link rel="stylesheet" type="text/css" href="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui.css" />
  <style>
    html {
      box-sizing: border-box;
      overflow: -moz-scrollbars-vertical;
      overflow-y: scroll;
    }
    *, *:before, *:after {
      box-sizing: inherit;
    }
    body {
      margin:0;
      background: #fafafa;
    }
  </style>
</head>
<body>
  <div id="swagger-ui"></div>
  <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-bundle.js"></script>
  <script src="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-standalone-preset.js"></script>
  <script>
    window.onload = function() {
      const ui = SwaggerUIBundle({
        url: './openapi.json',
        dom_id: '#swagger-ui',
        deepLinking: true,
        presets: [
          SwaggerUIBundle.presets.apis,
          SwaggerUIStandalonePreset
        ],
        plugins: [
          SwaggerUIBundle.plugins.DownloadUrl
        ],
        layout: "StandaloneLayout"
      });
    };
  </script>
</body>
</html>
  `;

  writeFileSync(join(docsDir, 'index.html'), htmlTemplate);

  // Generate Postman collection
  const postmanCollection = {
    info: {
      name: 'Bus Ticket Booking API',
      description: 'API collection for Bus Ticket Booking System',
      version: '1.0.0',
      schema: 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json'
    },
    auth: {
      type: 'bearer',
      bearer: [
        {
          key: 'token',
          value: '{{jwt_token}}',
          type: 'string'
        }
      ]
    },
    variable: [
      {
        key: 'base_url',
        value: 'http://localhost:3000',
        type: 'string'
      },
      {
        key: 'jwt_token',
        value: '',
        type: 'string'
      }
    ],
    item: []
  };

  writeFileSync(join(docsDir, 'postman-collection.json'), JSON.stringify(postmanCollection, null, 2));

  console.log('✅ API documentation generated successfully!');
  console.log('📁 Files created:');
  console.log('  - docs/openapi.json');
  console.log('  - docs/openapi.yaml');
  console.log('  - docs/index.html');
  console.log('  - docs/postman-collection.json');
  console.log('');
  console.log('🌐 To view the documentation:');
  console.log('  1. Open docs/index.html in your browser');
  console.log('  2. Or start the server and visit http://localhost:3000/api');

  await app.close();
}

generateApiDocs().catch(console.error);
