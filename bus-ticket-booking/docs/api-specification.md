# Bus Ticket Booking API Documentation

## Overview

The Bus Ticket Booking API is a comprehensive REST API for managing bus ticket bookings. It provides endpoints for user authentication, trip management, ticket booking, weather information, and administrative functions.

## Base URL

- **Development**: `http://localhost:3000`
- **Production**: `https://api.bus-booking.com`

## Authentication

The API uses JWT (JSON Web Token) for authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

### Getting a Token

1. Register a new user account or login with existing credentials
2. Use the `/auth/login` endpoint to obtain a JWT token
3. Include this token in subsequent requests

## Rate Limiting

- **Authenticated users**: 100 requests per minute
- **Unauthenticated users**: 20 requests per minute

## Error Handling

The API returns standard HTTP status codes and error responses in the following format:

```json
{
  "statusCode": 400,
  "timestamp": "2024-01-15T10:30:00.000Z",
  "path": "/api/endpoint",
  "method": "POST",
  "message": "Error description"
}
```

## Pagination

List endpoints support pagination with query parameters:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10, max: 100)

## API Endpoints

### Authentication

#### POST /auth/register
Register a new user account.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123",
  "firstName": "John",
  "lastName": "Doe"
}
```

#### POST /auth/login
Login with existing credentials.

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

**Response:**
```json
{
  "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": "uuid",
    "email": "<EMAIL>",
    "firstName": "John",
    "lastName": "Doe",
    "role": "USER"
  }
}
```

### Trips

#### GET /trips/search
Search for available trips.

**Query Parameters:**
- `departure` (required): Departure city
- `destination` (required): Destination city
- `date` (required): Travel date (YYYY-MM-DD)
- `page` (optional): Page number
- `limit` (optional): Items per page

**Response:**
```json
{
  "trips": [
    {
      "id": "uuid",
      "departure": "New York",
      "destination": "Boston",
      "departureTime": "2024-01-15T10:00:00Z",
      "arrivalTime": "2024-01-15T14:00:00Z",
      "price": 50.99,
      "availableSeats": 25,
      "totalSeats": 40,
      "driver": {
        "name": "John Driver",
        "licenseNumber": "DL123456"
      }
    }
  ],
  "total": 1,
  "page": 1,
  "limit": 10
}
```

#### POST /trips (Admin Only)
Create a new trip.

**Headers:**
```
Authorization: Bearer <admin-jwt-token>
```

**Request Body:**
```json
{
  "departure": "New York",
  "destination": "Boston",
  "departureTime": "2024-01-15T10:00:00Z",
  "arrivalTime": "2024-01-15T14:00:00Z",
  "price": 50.99,
  "totalSeats": 40,
  "driverId": "driver-uuid"
}
```

### Tickets

#### POST /tickets/book
Book a ticket for a trip.

**Headers:**
```
Authorization: Bearer <user-jwt-token>
```

**Request Body:**
```json
{
  "tripId": "trip-uuid",
  "seatId": "seat-uuid"
}
```

**Response:**
```json
{
  "id": "ticket-uuid",
  "tripId": "trip-uuid",
  "seatId": "seat-uuid",
  "status": "BOOKED",
  "bookingDate": "2024-01-15T10:30:00Z",
  "trip": {
    "departure": "New York",
    "destination": "Boston",
    "departureTime": "2024-01-15T10:00:00Z"
  },
  "seat": {
    "seatNumber": "A01"
  }
}
```

#### DELETE /tickets/:id/cancel
Cancel a booked ticket.

**Headers:**
```
Authorization: Bearer <user-jwt-token>
```

**Response:**
```json
{
  "id": "ticket-uuid",
  "status": "CANCELLED"
}
```

#### GET /tickets/my-bookings
Get current user's bookings.

**Headers:**
```
Authorization: Bearer <user-jwt-token>
```

### Weather

#### GET /weather/:city
Get weather information for a city.

**Parameters:**
- `city`: City name (e.g., "New York")

**Response:**
```json
{
  "city": "New York",
  "country": "US",
  "temperature": 22.5,
  "feelsLike": "Feels like 25°C",
  "description": "clear sky",
  "main": "clear",
  "humidity": 65,
  "pressure": 1013,
  "windSpeed": 5.2,
  "windDirection": "SW",
  "visibility": 10,
  "timestamp": "2024-01-15T10:30:00Z",
  "source": "api"
}
```

### Admin - Drivers

#### POST /admin/drivers (Admin Only)
Create a new driver.

**Headers:**
```
Authorization: Bearer <admin-jwt-token>
```

**Request Body:**
```json
{
  "name": "John Driver",
  "licenseNumber": "DL123456",
  "phoneNumber": "+**********",
  "email": "<EMAIL>"
}
```

#### GET /admin/drivers (Admin Only)
Get all drivers.

**Headers:**
```
Authorization: Bearer <admin-jwt-token>
```

## Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `409` - Conflict (e.g., seat already booked)
- `429` - Too Many Requests
- `500` - Internal Server Error
- `503` - Service Unavailable

## Concurrency Handling

The API implements several mechanisms to handle concurrent requests safely:

1. **Database Transactions**: All booking operations are wrapped in database transactions
2. **Pessimistic Locking**: Seat reservations use pessimistic locking to prevent double-booking
3. **Optimistic Locking**: Version control on entities to detect concurrent modifications
4. **Idempotency**: Support for idempotency keys on critical operations

## Security Features

- JWT-based authentication
- Role-based access control (USER, ADMIN)
- Rate limiting to prevent abuse
- Input validation and sanitization
- CORS configuration
- Secure password hashing

## Caching

- Weather data is cached for 30 minutes using Redis
- API responses include cache headers where appropriate
- Distributed caching for improved performance

## Testing

The API includes comprehensive test coverage:

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:cov

# Run specific test files
npm test -- --testPathPattern=auth
npm test -- --testPathPattern=trips
npm test -- --testPathPattern=tickets
npm test -- --testPathPattern=weather
```

## Development

### Prerequisites
- Node.js v18+
- PostgreSQL v13+
- Redis v6+

### Setup
1. Clone the repository
2. Install dependencies: `npm install`
3. Set up environment variables (see .env.example)
4. Run database migrations: `npm run migration:run`
5. Start the development server: `npm run start:dev`

### Docker Development
```bash
# Start all services
npm run docker:dev

# Stop all services
npm run docker:dev:down
```
